{"_id": {"$oid": "6875463d4d78408596680569"}, "name": "Dragon", "category": "fruit", "fruitData": {"statsData": {"Normal": [{"key": "TAP", "name": "Normal Attack", "damage": {"type": "unknown", "reason": "Not specified in official wiki", "display": "Unknown", "note": "Community testing may provide approximate values"}, "cooldown": 0, "energy": {"type": "percentage", "value": 0, "display": "0%"}}, {"key": "Z", "name": "Heatwave Cannon", "damage": {"type": "unknown", "reason": "Not specified in official wiki", "display": "Unknown", "note": "Community testing may provide approximate values"}, "cooldown": 40, "energy": {"type": "percentage", "value": 4, "display": "4%"}}, {"key": "X", "name": "Infernal Pincer", "damage": {"type": "unknown", "reason": "Not specified in official wiki", "display": "Unknown", "note": "Community testing may provide approximate values"}, "cooldown": 60, "energy": {"type": "percentage", "value": 6, "display": "6%"}}, {"key": "C", "name": "Scorching Downfall", "damage": {"type": "unknown", "reason": "Not specified in official wiki", "display": "Unknown", "note": "Community testing may provide approximate values"}, "cooldown": 100, "energy": {"type": "range", "min": 9, "max": 19, "unit": "%", "display": "9-19%", "average": 14}}, {"key": "V", "name": "Imperial Evolution", "damage": {"type": "unknown", "reason": "Not specified in official wiki", "display": "Unknown", "note": "Community testing may provide approximate values"}, "cooldown": 10, "energy": {"type": "conditional", "display": "15% (Hybrid) <br> 30% (Full Form)", "variants": [{"value": "15%", "condition": "Hybrid", "display": "15% (Hybrid)"}, {"value": "30%", "condition": "Full Form", "display": "30% (Full Form)"}]}}, {"key": "F", "name": "Dracon<PERSON> Soar", "damage": {"type": "unknown", "reason": "Not specified in official wiki", "display": "Unknown", "note": "Community testing may provide approximate values"}, "cooldown": 50, "energy": {"type": "percentage", "value": 2.5, "display": "2.5%"}}]}, "overviewData": {"Transformed (East)": {"pros": ["Hard to control when transformed", "High damage reduction.", "Shorter cooldowns.", "Good grinding fruit.", "Alright for PvP.", "Smaller hitbox compared to the Western form.", "Can solo raids with enough time", "Can damage Sea Beast and <PERSON><PERSON><PERSON>.", "Continuous AoE attack.", "Highest range in the game if held.", "Large hitbox.", "High knockback.", "Applies burn damage over time.", "High stun that ensures the last hit always connects.", "Extremely large hitbox.", "Extremely high stun which drags enemies upwards.", "Cannot be cancelled by damage.", "The user can control the duration of the move for combos, unlike the West version.", "When used with Cyborg/Ghoul V4, it will make the player get full/ almost full health.", "Decent damage.", "Gives the user a long i-frame.", "Good combo potential.", "Very fast."], "cons": ["Very large hitbox, which makes the user vulnerable.", "Good coverage, but poor range relative to the player's character.", "Cursed Dual Katana [X] ignores the damage reduction.", "Weakens if used for a long time, shortening the flame distance.", "Requires some time to use fully recharged flames.", "<PERSON><PERSON> can be dodged in aerial combat.", "Drastically drains the fury meter when charging it up.", "Lack of range.", "Forces the user to stay in animation.", "The damage area is very specific and misses most of the time.", "Less effective in aerial combat due to the slow flying speed and low spin damage."]}, "Transformed (West)": {"pros": ["High damage reduction.", "Shorter cooldowns.", "Good grinding fruit.", "Insanely good for PvP.", "Can solo every raid with enough experience, including <PERSON><PERSON>.", "Fastest flight speed in the game.", "Damages Sea Beasts.", "Continuous AoE attack.", "Highest range in the game if held.", "Large hitbox.", "High knockback.", "Long range.", "Very good burst damage.", "Applies burn damage over time.", "Extremely large hitbox.", "Quick start-up.", "Can grab enemies in the middle of the attack.", "Aimable, unlike the East version.", "Decent damage and knockback.", "Large AoE.", "Good combo potential.", "Great mobility."], "cons": ["Very large hitbox.", "Cursed Dual Katana [X] partially ignores the damage reduction.", "Although the fastest speed, it still relies on the [F] due to its low sustained speed when flying upwards.", "Weakens if used for long time, shortening flame distance.", "Requires some time to use fully recharged flames.", "<PERSON><PERSON> can be dodged in aerial combat.", "Drastically drains the fury meter when charging it up.", "No general cons to speak of.", "The orbs are often shot too high and do not attract enemies.", "Drains the fury meter when holding down the move.", "Missing the move drains a significant amount of fury meter.", "Requires a solid surface to deal full damage.", "Goes downward after grabbing an enemy, which can make the user drown if used above water. (Although the damage is low)"]}}, "skillData": {"Normal": [], "Pros": []}, "passiveAbilities": [{"name": "Draconic Dominance", "description": "Fury Meter The user starts with a small \"Fury Meter\" that must be full to transform into a Dragon hybrid. The Orange meter refills slowly over time when not transformed and drains with any move used. The second Fury Meter fills only by dealing damage and must be full to transform into full Dragon Form. While transformed, the Fury meter begins draining for a short time, indicated by flames above it, and taking damage starts draining it again. However, the Fury meter doesn’t drain passively while transformed, allowing indefinite transformation as long as it is not emptied.", "showcaseUrl": null}, {"name": "Strong Hair/Saddle", "description": "Can be unlocked after getting 500 mastery on Dragon and talking to the Dragon Tamer NPC and paying 5,000. When transformed, the user can be mounted by other allied players. Maximum is 4 for Eastern Form and 5 for Western Form. (The purchase only applies to the currently equipped variant, and the user will have to make another purchase in the other variant for obtaining the passive on both variants.)", "showcaseUrl": null}, {"name": "Skins", "description": "The user can obtain other colors for their Dragon form by crafting them. They can also be bought using Robux, but Permanent Dragon must be owned. Every owned color can be selected inside the inventory in the \"Skins\" category. The user can also use five chromatic colors which can be obtained from the CHROMATIC bundle in the shop. * Green is unlocked and equipped by default. * The Orange, Yellow, Blue, Red, Purple, and Black colors are unlocked crafting them with the Barista NPC, or buying them with Robux by owning the Permanent Dragon Fruit. * The Blood Moon, Eclipse, Ember, Phoenix Sky, and Violet Night chromatic skins can be bought in the Shop with Robux for a limited amount of time. (The only skin that has been sold out is currently Eclipse, which could be bought for 1999.) Images of the different colored Dragon forms can be found here.", "showcaseUrl": null}, {"name": "Draconic Blood", "description": "In Hybrid form, the user gains a"}, {"name": "Draconic Monarch's Scales", "description": "Draconic Monarch's Scales * In Dragon form, the user gains a"}], "variantData": {"eastern": {"name": "Eastern Dragon", "theme": "East Asian mythology", "mountingCapacity": 4, "specialMechanics": ["spinning attacks", "upward spirals"], "movementStyle": "aerial maneuverability", "culturalReference": "East Asian dragon mythology"}, "western": {"name": "Western Dragon", "theme": "European medieval", "mountingCapacity": 5, "specialMechanics": ["claw attacks"], "movementStyle": "direct assault", "culturalReference": "Medieval European dragon"}}, "damageResistance": {"transformed": "53%", "hybrid": {"players": "34%", "npcs": "34%"}, "human": "0%"}, "mainDescription": "Dragon is a Mythical Beast-type Blox Fruit that costs 15,000,000 or 5,000 from the Blox Fruit Dealer. Dragon allows the user to gain the power, and transform to a mythical Dragon. The user can transform either to an Eastern Dragon; resembling those from the East Asian culture, or the Western Dragon; resembling a medieval armored Dragon from European folklore. The user can also transform into a large, tall human-hybrid dragon that deals more massive fiery damage. Dragon in its base form is usually considered decent for PvE (grinding) and sufficient for PvP due to its high damage and large AoE. When transformed into hybrid form, its moves are even further enhanced, and its grinding potential becomes almost on the same level as <PERSON> due to [M1] combos being extended to five instead of three slashes. When fully transformed, <PERSON> is one of the best Blox Fruits for PvE and PvP due to its high damage, huge AoE, Instinct-breaking moves, mobility, and more. However, sky camping and stunning moves are a weakness of this fruit. The fully transformed form of the fruit is likely to win most fights, although this can be considered subjective due to varying skill levels of opponents, and the user themselves. Dragon has two versions: one being the Western Variant and the other being the Eastern Variant, each with different damage outputs, animations and appearances while transformed. It also has a hybrid form for both versions when a player uses V after the orange Fury Meter (first Fury Meter) is full and before the red Fury Meter (second Fury Meter) is full.", "type": "Beast", "rarity": "Mythical", "introducedUpdate": "13", "m1Capability": true, "awakening": false, "combatRating": {"pvp": "Good", "grinding": "Good"}, "priceData": {"current": {"money": 15000000, "status": "AVAILABLE", "robux": 5000}, "historical": [{"money": 3500000, "context": "Changed from 3.500.000 to 15.000.000"}, {"robux": 2600, "context": "Pre-rework price"}, {"money": 3500000, "update": "17.3", "context": "Before Update 17.3"}], "discounts": [{"originalPrice": 5000, "discountedPrice": 2400, "type": "robux", "context": "Permanent Dragon Token discount"}], "sources": ["Blox Fruit Dealer", "Dragon Egg", "Kitsune Shrine", "Prehistoric Island", "Blox Fruit Gacha", "Shop", "Sea Event"]}, "transformation": true, "furyMeterMechanics": {"orangeMeter": {"fillMethod": "time-based", "requiredFor": "hybrid transformation", "drainTriggers": ["move usage", "taking damage"]}, "redMeter": {"fillMethod": "damage-based", "requiredFor": "full transformation", "drainTriggers": ["taking damage"]}}, "masteryRequirements": {"Heatwave Cannon": 1, "Infernal Pincer": 150, "Scorching Downfall": 250, "Imperial Evolution": 350, "Draconic Soar": 75}, "forms": [{"name": "Normal", "type": "Normal", "moves": [{"key": "TAP", "name": "Normal Attack", "description": "The user begins to scratch their opponent with fiery claws three times, each scratch with differing directions. This move is similar to the ones of <PERSON><PERSON><PERSON> and T-Rex.", "mastery": null, "gif": "Dragon_M1", "damage": null, "cooldown": "0", "energy": "0%", "furyMeter": null}, {"key": "Z", "name": "Heatwave Cannon", "description": "The user fires a beam of scorching hot energy, knocking enemies back. Holding this move will make this move have more range and damage. It can be held for around 5-6 seconds. While using this attack, the user floats, so they won't fall.", "mastery": 1, "gif": "Dragon_ZA", "gif1": "Dragon_ZB", "damage": null, "cooldown": "40", "energy": "4%", "furyMeter": null}, {"key": "X", "name": "Infernal Pincer", "description": "When held, the user prepares themselves to launch towards their opponent. As the key is released, the user will dash at their opponent and drag them before full on throwing them at their cursor smashing into the ground. This move deals 2 ticks of burn afterwards. This move is similar to Eagle's [C] <PERSON>er, except the user can aim the slam.", "mastery": 150, "gif": "Dragon_XA", "gif1": "Dragon_XB", "damage": null, "cooldown": "60", "energy": "6%", "furyMeter": null}, {"key": "C", "name": "Scorching Downfall", "description": "The user leaps into the air and begins bombarding the ground with fiery projectiles. Upon impact, the projectiles explode, damaging enemies caught in the vicinity, knocking them back, and stunning them for a brief moment. Holding this move allows the user to shoot fireballs for around 2.5 seconds. After an individual enemy reaches a certain threshold of damage from fireballs, they will take greatly reduced damage from fireballs. This threshold is reached after about half of the total fireballs hit the enemy, meaning the user can miss some of their fireballs or end their skill early and still deal most of the damage. This move is similar to unawakened Light's [X] move.", "mastery": 250, "gif": "Dragon_C", "damage": null, "cooldown": "100", "energy": "9-19%", "furyMeter": null}, {"key": "V", "name": "Imperial Evolution", "description": "If the user has acquired all of the orange fury meter bar, they will proceed to evolve into hybrid form. But if the user has the red fury meter bar full, they will transform into a dragon depending on their choice of east or west. However, the user cannot use fighting styles, swords, and guns while transformed.", "mastery": 350, "gif": "Dragon_VA", "gif1": "Dragon_VB", "gif2": "Dragon_VC", "damage": null, "cooldown": "10", "energy": "15% (Hybrid) <br> 30% (Full Form)", "furyMeter": null}, {"key": "F", "name": "Dracon<PERSON> Soar", "description": "The user grows dragon wings and lunges at their desired opponent. If the attack lands, the player will grab the opponent, damage them and dash away (the user can keep flying after hitting by holding the move). If not, they will be able to soar, hence the name. The user will fly faster when aiming downward.", "mastery": 75, "gif": "Dragon_FA", "gif2": "Dragon_FB", "damage": null, "cooldown": "50", "energy": "2.5%", "furyMeter": null}], "images": {"fruit": "Dragon Fruit.png|Fruit Icon Dragon.png|Icon"}}, {"name": "Hybrid Form", "type": "Hybrid", "moves": [{"key": "TAP", "name": "Normal Attack", "description": "The user attacks their desired opponent with scorching scratches five times. The hybrid version's range is a lot better, and the slashes also set anyone hit on fire for a short period.", "mastery": null, "gif": "Dragon_Hybrid_M1", "damage": null, "cooldown": "0", "energy": "0%", "furyMeter": null}, {"key": "Z", "name": "Heatwave Cannon", "description": "The user violently fires a beam of scorching energy at their desired opponent (or their cursor), knocking enemies backward. If the key is held, the move becomes significantly more powerful, dealing even more damage and range.", "mastery": 1, "gif": "Dragon_Hybrid_ZA", "gif1": "Dragon_Hybrid_ZB", "damage": null, "cooldown": "40", "energy": "4%", "furyMeter": null}, {"key": "X", "name": "Infernal Pincer", "description": "When held, the user will prep themselves to dash. Upon release, the user (depending on if they land the attack or not) will lunge towards their opponent accompanied by a trail of flames and slam them at their cursor. This move deals 2 ticks of burn afterwards.", "mastery": 150, "gif": "Dragon_Hybrid_XA", "gif1": "Dragon_Hybrid_XB", "damage": null, "cooldown": "60", "energy": "6%", "furyMeter": null}, {"key": "C", "name": "Scorching Downfall", "description": "The user leaps into the air and bombards the ground with multiple fiery projectiles. Upon impact, the projectiles explode, damaging enemies caught in the vicinity, knocking them back, and stunning them for a brief moment. Holding this move allows the user to shoot fireballs for around 4 seconds. After an individual enemy reaches a certain threshold of damage from fireballs, they will take greatly reduced damage from fireballs. This threshold is reached after about half of the total fireballs hit the enemy, meaning the user can miss some of their fireballs or end their skill early and still deal most of the damage. The hybrid version lets the user hold it for longer, meaning more damage can be applied.", "mastery": 250, "gif": "Dragon_Hybrid_C", "damage": null, "cooldown": "100", "energy": "9-24%", "furyMeter": null}, {"key": "V", "name": "Imperial Evolution", "description": "Depending on if the user's fury meter is full or not, the user will transform into either their human form or their Dragon form.", "mastery": 350, "gif": "Dragon_Hybrid_VA", "gif1": "Dragon_Hybrid_VB", "gif2": "Dragon_Hybrid_VC", "damage": null, "cooldown": "10", "energy": "0% (Normal) <br> 30% (Full Form)", "furyMeter": null}, {"key": "F", "name": "Dracon<PERSON> Soar", "description": "The user sprouts out draconic wings out of their back and lunge forward. If the attack lands, the user will grab said person and slash them away (the user can keep flying after hitting by holding the move). If nobody is hit, they will be able to continue their soar. The user usually flies faster when aiming downward, and if the move hits the flight won't be interrupted.", "mastery": 75, "gif": "Dragon_Hybrid_FA", "gif1": "Dragon_Hybrid_FB", "damage": null, "cooldown": "50", "energy": "2.5%", "furyMeter": null}], "images": {"fruit": "Dragon Fruit.png|Fruit Icon Dragon.png|Icon"}}, {"name": "Transformed (East)", "type": "Transformed", "moves": [{"key": "TAP", "name": "Normal Attack", "description": "The player opens their mouth and begins to blast fire at their cursor for around 2 seconds before completely depleting. (Can be held for longer, but the range will decrease rapidly.)", "mastery": null, "gif": "Dragon_East_M1", "damage": null, "cooldown": "0", "energy": "3-15.5%", "furyMeter": null}, {"key": "Z", "name": "Heatwave Cannon", "description": "The user, with great force, charges a beam from their mouth (depending on if the move is held) and will violently fire a beam of hot energy. Whereas if the beam is fully charged it causes the rings to glow purple. This move possesses an extreme range and big AoE. The user can move around while charging this move.", "mastery": 1, "gif": "Dragon_East_ZA", "gif1": "Dragon_East_ZB", "damage": null, "cooldown": "40", "energy": "9-39%", "furyMeter": null}, {"key": "X", "name": "Infernal Pincer", "description": "The user spins in an upward motion, grabbing anyone unlucky enough to be within vicinity. The user then dives downwards, causing a massive explosion and leaving debris everywhere.", "mastery": 150, "gif": "Dragon_East_X", "damage": null, "cooldown": "60", "energy": "15.5%", "furyMeter": null}, {"key": "C", "name": "Scorching Downfall", "description": "The user spins upwards, albeit much slower and in a wider space than the previous move, during which a firestorm will accompany the user's spin. At the end, the user reaches peak altitude, and a purple explosion finishes off the move. The length of this move depends on how long it is held.", "mastery": 250, "gif": "Dragon_East_C", "damage": null, "cooldown": "100", "energy": "17-25%", "furyMeter": null}, {"key": "V", "name": "Imperial Evolution", "description": "The user turns back to their normal form (or hybrid if they were in that state before turning into a Dragon).", "mastery": 350, "gif": "Dragon_East_VA", "gif1": "Dragon_East_VB", "damage": null, "cooldown": "10", "energy": "0%", "furyMeter": null}, {"key": "F", "name": "Dracon<PERSON> Soar", "description": "The user aims at their cursor and begins spinning. The user gains speed aiming downward, and can even crash onto solid surfaces which causes an large, fiery explosion. Attempting to crash into the ocean will damage non-shark users, including the users themselves.", "mastery": 75, "gif": "Dragon_East_FA", "gif1": "Dragon_East_FB", "damage": null, "cooldown": "50", "energy": "9-13.3%", "furyMeter": null}], "images": {"fruit": "Dragon (East) Fruit.png|Fruit Icon Dragon (East)Fruit Gif.gif|Fruit GIF Dragon (East).png|Icon"}}, {"name": "Transformed (West)", "type": "Transformed", "moves": [{"key": "TAP", "name": "Normal Attack", "description": "The user creates fire out of its mouth, and it lasts for around two seconds before running out. (Can be held for longer, but the range will decrease heavily.)", "mastery": null, "gif": "Dragon_West_M1", "damage": null, "cooldown": "0", "energy": "3-15.5%", "furyMeter": null}, {"key": "Z", "name": "Heatwave Cannon", "description": "The user shoots a huge beam of fire from their mouth. When the move is fully charged it causes a ring of fire appear in front of the player's head. This move possesses a considerable range and big AoE. Unlike the East variant, the player is unable to move whilst charging this move.", "mastery": 1, "gif": "Dragon_West_ZA", "gif1": "Dragon_West_ZB", "damage": null, "cooldown": "40", "energy": "9-39%", "furyMeter": null}, {"key": "X", "name": "Infernal Pincer", "description": "The user uses their claws to create huge slashes, similar to the classic Dragon's and Sanguine Art's X move but a lot better in visuals, speed, AoE and damage.", "mastery": 150, "gif": "Dragon_West_X", "damage": null, "cooldown": "60", "energy": "13.3%", "furyMeter": null}, {"key": "C", "name": "Scorching Downfall", "description": "The user shoots an orb which rains meteors downwards, doing very high damage and having an explosion at the end.", "mastery": 250, "gif": "Dragon_West_C", "damage": null, "cooldown": "100", "energy": "18%", "furyMeter": null}, {"key": "V", "name": "Imperial Evolution", "description": "The user turns back to their normal avatar (or hybrid if they were in that state before turning into a Dragon).", "mastery": 350, "gif": "Dragon_West_VA", "gif1": "Dragon_West_VB", "damage": null, "cooldown": "10", "energy": "0%", "furyMeter": null}, {"key": "F", "name": "Dracon<PERSON> Soar", "description": "The user picks up an opponent, slices them, throws them back to the ground, and then slams themselves onto them. If not hitting an opponent, the user dashes, creating a cone of fire in front of them, propelling them forwards faster than their original flying speed.", "mastery": 75, "gif": "Dragon_West_FA", "gif1": "Dragon_West_FB", "damage": null, "cooldown": "50", "energy": "9%", "furyMeter": null}], "images": {"fruit": "Dragon (West) Fruit.png|Fruit Icon Dragonwest.gif|Fruit GIF Dragon (West).png|Icon"}}], "trivia": ["The Dragon Fruit can also be obtained from the Dragon Egg Dragon Eggs at the Prehistoric Island with an extremely low chance, being able to get either the East or West variants.", "Dragon is the most expensive and difficult-to-obtain fruit in the game.", "Before Update 24, <PERSON> was the third most expensive fruit in the game, with the first being <PERSON><PERSON><PERSON> and the second being <PERSON><PERSON>.", "Before Update 17.3, Dragon was once again the most expensive fruit in the game, valued 3,500,000. This is the second time that Dragon is the most valuable fruit.", "Pre-rework, Dragon costed 2,600, which increased by 92.3% up to 5,000 post-rework, making it by far the highest Robux price increase of any purchasable item in the game.", "Similarly, it also has the highest Money price increase of any reworked item, going from 3,500,000 to 15,000,000. According to the calculations, the price of that is increased by 328.57%.", "Mastery gained will be common to both Dragon variations.", "Dragon has the largest transformed model in the game.", "<PERSON>'s Western version is also similar to a fan-made Wyvern fruit showcased in the 2024 fan art competition.", "Additionally, in most Chinese myths, Eastern Dragons are depicted to have control over the weather, thus allowing them to wield water, thunder and wind.", "<PERSON> and <PERSON><PERSON><PERSON> are the only beast fruits that, while transformed, can carry players on their backs. However, <PERSON> needs <PERSON><PERSON>/<PERSON> Hair to carry someone on their back.", "Dragon and Kitsune are the only fruits that can be obtained from Sea Events such as the Kitsune Shrine and Prehistoric Island.", "The user can measure how fast they are moving using the Dragon's Hybrid form. The higher the speed, the higher the volume and pitch of the footsteps of the hybrid.", "Dragon is the first fruit to have two versions on its transformation and fruit model.", "Dragon has the highest damage reduction in the game."], "recommendations": ["There is a glitch where discounted Dragon appears on the recommended fruits bar."], "variantsComplete": {"eastern": {"name": "Eastern Dragon", "theme": "East Asian mythology", "mountingCapacity": 4, "specialMechanics": ["spinning attacks", "upward spirals"], "movementStyle": "aerial maneuverability", "culturalReference": "East Asian dragon mythology"}, "western": {"name": "Western Dragon", "theme": "European medieval", "mountingCapacity": 5, "specialMechanics": ["claw attacks"], "movementStyle": "direct assault", "culturalReference": "Medieval European dragon"}}, "variants": [{"name": "Eastern Dragon", "type": "East", "mountingCapacity": 4, "flightSpeed": null, "damageResistance": "53%", "specialFeatures": ["Player mounting", "Damage resistance", "East Asian dragon design", "Spinning attacks"]}, {"name": "Western Dragon", "type": "West", "mountingCapacity": 5, "flightSpeed": null, "damageResistance": "53%", "specialFeatures": ["Player mounting", "Damage resistance", "Medieval armored dragon design", "Claw attacks"]}], "reworkDetails": "Dragon Token Upon the release of Update 24, the old Dragon fruits (physical and equipped) were converted into Dragon Tokens, which could be exchanged for a random Mythical fruit, (including Dragon East and West) when used. Players who got it could also use the Dragon (Classic) if they had a Permanent Dragon Token. Permanent Dragon fruits were instead converted into Permanent Dragon Tokens, which could be exchanged for one of these four sets of items: * Upgrade: A discount on purchasing the new permanent Dragon (making it 2,400 instead of 5,000). * Fruits: A Permanent Dough alongside a Permanent Flame. * Game Passes: Some Game Passes, such as x2 Money, x2 Drops, x2 Mastery, Fast Boats, +1 Fruit Storage (3x), and Fragments. * Swordsman: A Dark Blade, 2 Mythical and 2 Legendary Scrolls, and some Fragments. After heavy backlash from the community, the Dragon Tokens were changed three days after the update (December 17, 2024). * After the change, everyone who had a physical Dragon were given one of either an Eastern or Western Dragon in physical form, with the rest of the regular Dragon Tokens being changed to randomly exchange to any of these Mythical fruits (with an equal chance): <PERSON><PERSON>, <PERSON><PERSON>m, Gas, <PERSON>pard, Kit<PERSON>e and Dragon. * Permanent Dragon Tokens were instead removed, and everyone who had a permanent Dragon were instead able to choose to get either the Eastern or Western version permanently. * The old version of Dragon was made unobtainable.", "skinSystem": {"defaultSkin": "Green", "craftableSkins": ["Orange", "Yellow", "Blue", "Red", "Purple", "Black"], "chromaticSkins": ["Blood Moon", "Eclipse", "Ember", "Phoenix Sky", "Violet Night"], "acquisition": {"crafting": "Barista NPC", "purchase": "<PERSON><PERSON> (permanent required)", "limited": "Shop bundles"}, "prices": {"Eclipse": 1999}}, "skins": [{"name": "Green", "type": "<PERSON><PERSON><PERSON>", "price": 1999}, {"name": "Orange", "type": "Craftable"}, {"name": "Blood Moon", "type": "Chromatic"}], "changeHistoryAdvanced": [{"update": "26", "changes": [{"description": "Buffed the Fury Meter gain from landing skills on base or hybrid mode.", "type": "buff"}, {"description": "Nerfed Dragon with the changes seen below:", "type": "nerf"}, {"description": "Reduced the fury meter gain from landing skills on transformation.", "type": "general"}, {"description": "Reduced transformed damage resistance from 60% to 53%.", "type": "general"}, {"description": "Increase cooldowns for all moves (+0.5 seconds on each move).", "type": "general"}, {"description": "Reduced damage of all moves (-5% on each move).", "type": "general"}, {"description": "Slightly decreased Western Dragon transformation's flight speed.", "type": "nerf"}], "type": "balance"}, {"update": "24", "changes": [{"description": "Dragon was remade available to purchase.", "type": "general"}, {"description": "Dragon was reworked into two versions: Western and Eastern.", "type": "general"}, {"description": "Added Dragon's Hybrid Form.", "type": "addition"}, {"description": "Added an M1 attack.", "type": "addition"}, {"description": "[Z] Heatwave Beam was renamed to [Z] Heatwave Cannon.", "type": "general"}, {"description": "[X] Draconic Claw was renamed to [X] Infernal Pincer.", "type": "general"}, {"description": "[C] Fire Shower was renamed to [C] Scorching Downfall.", "type": "general"}, {"description": "[V] Transformation was renamed to [V] Imperial Evolution.", "type": "general"}, {"description": "[F] Dragon Flight was renamed to [F] Draconic Soar.", "type": "general"}], "type": "major_rework"}, {"update": "23", "changes": [{"description": "Dragon was made unavailable to purchase from the Blox Fruit Dealer and Shop.", "type": "general"}, {"description": "Its price was changed to \"IN PROGRESS\".", "type": "general"}, {"description": "It started getting reworked.", "type": "general"}], "type": "major_rework"}, {"update": "20", "changes": [{"description": "Dragon Fruit's model icon was remade.", "type": "general"}], "type": "general"}, {"update": "17.3", "changes": [{"description": "[C] Fire Shower and [X] Dragonic Claw were reworked.", "type": "general"}, {"description": "Fire Shower's hitbox was reduced.", "type": "nerf"}, {"description": "Both of these abilities got a small VFX rework.", "type": "general"}], "type": "major_rework"}, {"update": "13", "changes": [{"description": "Dragon was released.", "type": "general"}], "type": "release"}], "economicData": {"acquisitionMethods": [{"source": "Dragon Egg", "location": "Prehistoric Island", "chance": "extremely low"}, {"source": "Kitsune Shrine", "type": "Sea Event"}, {"source": "Blox Fruit Dealer", "type": "Shop"}, {"source": "Blox Fruit Gacha", "type": "Random"}], "reworkHistory": {"majorUpdate": "Update 24", "tokenSystem": "Dragon Token conversion", "priceIncrease": {"robux": "92.3%"}}, "competitiveRanking": {"difficulty": "Highest in game", "grinding": "S-tier when transformed", "pvp": "S-tier when transformed"}, "marketAnalysis": {"pricePosition": "Most expensive fruit", "availability": "Extremely rare"}}, "gallery": [{"url": "Hybrid Transform.png", "caption": "Hybrid Form"}, {"url": "DragonEast38242342.png", "caption": "Eastern Dragon Form"}, {"url": "DragonEast3422423423.png", "caption": "Eastern Dragon Form (Aura)"}, {"url": "DragonWest233.png", "caption": "Western Dragon Form"}, {"url": "DragonWest3489.png", "caption": "Western Dragon Form (Aura)"}, {"url": "East Dragon Concept Artist.jpeg", "caption": "Eastern Dragon concept artist was created by @Tobey_D_Artist (on X/Twitter)."}, {"url": "RedgreenandblueEastern.jpg", "caption": "Eastern dragon 3D model was created by @oreI_orL (on X/Twitter)."}, {"url": "RedgreenandblueWestern.jpg", "caption": "Western dragon 3D model was created by @oreI_orL (on X/Twitter)."}, {"url": "Eastern and Western dragon.png", "caption": "Both Eastern and Western Dragons together."}], "shopQuote": "Transforms the user into a mighty Dragon, allowing them to rule over the skies with scorching flames."}, "imageUrl": "https://static.wikia.nocookie.net/roblox-blox-piece/images/a/a0/Fruit.png/revision/latest?cb=20200104053347", "imageUrls": ["https://static.wikia.nocookie.net/roblox-blox-piece/images/a/a0/Fruit.png/revision/latest?cb=20200104053347", "https://static.wikia.nocookie.net/roblox-blox-piece/images/2/29/Dragon_Fruit.png/revision/latest?cb=20241218114129", "https://static.wikia.nocookie.net/roblox-blox-piece/images/e/e2/DragonMeter.gif/revision/latest?cb=20250102193554", "https://static.wikia.nocookie.net/roblox-blox-piece/images/8/8a/Hybrid_Transform.png/revision/latest?cb=20250211183633", "https://static.wikia.nocookie.net/roblox-blox-piece/images/2/2b/DragonWest233.png/revision/latest?cb=20250211184106"], "lastUpdated": {"$date": "2025-07-14T18:02:37.345Z"}, "rawData": {"infobox": {"name": "Dragon", "tab1": "All", "image1": "Dragon Fruit.png|Fruit Icon Dragon.png|Icon", "tab2": "East", "image2": "Dragon (East) Fruit.png|Fruit Icon Dragon (East)Fruit Gif.gif|Fruit GIF Dragon (East).png|Icon", "tab3": "West", "image3": "Dragon (West) Fruit.png|Fruit Icon Dragonwest.gif|Fruit GIF Dragon (West).png|Icon", "type": "Beast", "rarity": "Mythical", "m1": "Yes", "update": "13", "money": "15,000,000"}, "wikitextLength": 30571, "movesFound": 48, "statsFound": 24, "extractedAt": "2025-07-14T18:02:37.345Z"}, "type": "fruit", "wikiUrl": "https://blox-fruits.fandom.com/wiki/Dragon"}