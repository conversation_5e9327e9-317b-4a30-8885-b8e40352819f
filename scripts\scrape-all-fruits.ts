import * as dotenv from "dotenv"
import { FruitScraper } from "../lib/scrapers/scrapers/fruit-scraper"
import { MongoClient } from "mongodb"

// Charger les variables d'environnement
dotenv.config({ path: ".env.local" })

interface FruitScrapingStats {
  totalAttempts: number
  successfulScrapes: number
  failedScrapes: number
  fruitsWithForms: number
  fruitsWithAwakening: number
  fruitsWithMoveSets: number
  fruitsWithStats: number
  averageFormsPerFruit: number
  averageMovesPerForm: number
  errors: string[]
  duration: number
}

async function scrapeAllFruits() {
  const mongoUrl = process.env.MONGODB_URL
  if (!mongoUrl) {
    console.error("❌ MONGODB_URL environment variable is required")
    console.log("💡 Create a .env.local file with:")
    console.log("MONGODB_URL=mongodb://localhost:27017/bloxfruits")
    console.log("or your MongoDB connection string")
    process.exit(1)
  }

  const startTime = Date.now()
  const stats: FruitScrapingStats = {
    totalAttempts: 0,
    successfulScrapes: 0,
    failedScrapes: 0,
    fruitsWithForms: 0,
    fruitsWithAwakening: 0,
    fruitsWithMoveSets: 0,
    fruitsWithStats: 0,
    averageFormsPerFruit: 0,
    averageMovesPerForm: 0,
    errors: [],
    duration: 0,
  }

  console.log("🍎 BLOX FRUITS SPECIALIZED SCRAPER")
  console.log("===================================")
  console.log("Using our enhanced FruitScraper with all tabber/stats/recommendation fixes")
  console.log("This will scrape ALL fruits from the Blox_Fruits wiki category")
  console.log("")

  const fruitScraper = new FruitScraper()
  const client = new MongoClient(mongoUrl)

  try {
    await client.connect()
    const db = client.db("bloxfruits")
    const collection = db.collection("fruits")

    // Get all available fruits first
    console.log("🔍 Getting list of all available fruits...")
    const members = await fruitScraper.getCategoryMembers("Blox_Fruits")
    console.log(`Found ${members.length} fruits in Blox_Fruits category`)
    console.log("")

    if (members.length === 0) {
      console.warn("⚠️ No fruits found in category. Exiting.")
      return
    }

    // Display the fruits that will be scraped
    console.log("📋 Fruits to scrape:")
    members.forEach((member, index) => {
      console.log(`  ${(index + 1).toString().padStart(2)}. ${member.title}`)
    })
    console.log("")

    // Clear existing fruits
    console.log("🗑️ Clearing existing fruits from database...")
    await collection.deleteMany({})

    // Scrape each fruit
    const scrapedFruits = []
    stats.totalAttempts = members.length

    for (let i = 0; i < members.length; i++) {
      const member = members[i]
      const fruitName = member.title
      const progress = `(${i + 1}/${members.length})`

      try {
        console.log(`\n🍎 ${progress} Scraping ${fruitName}...`)
        const fruit = await fruitScraper.scrapeItem(fruitName, "fruit")

        if (fruit && fruit.fruitData) {
          scrapedFruits.push(fruit)
          stats.successfulScrapes++

          // Analyze the scraped data
          const forms = fruit.fruitData.forms || []
          const totalMoves = forms.reduce((total, form) => total + (form.moves?.length || 0), 0)

          if (forms.length > 0) stats.fruitsWithForms++
          if (fruit.fruitData.awakening) stats.fruitsWithAwakening++
          if (totalMoves > 0) stats.fruitsWithMoveSets++
          // Check if any moves have stats (damage, cooldown, energy)
          const hasStats = forms.some(form => 
            form.moves?.some(move => 
              move.damage || move.cooldown || move.energy || move.furyMeter
            )
          )
          if (hasStats) stats.fruitsWithStats++

          // Display quick stats
          console.log(`  ✅ Success: ${fruit.name}`)
          console.log(`     Type: ${fruit.fruitData.type || 'Unknown'}`)
          console.log(`     Rarity: ${fruit.fruitData.rarity || 'Unknown'}`)
          console.log(`     Forms: ${forms.length}`)
          console.log(`     Total Moves: ${totalMoves}`)
          console.log(`     Awakening: ${fruit.fruitData.awakening ? 'Yes' : 'No'}`)
          
          if (forms.length > 0) {
            console.log(`     Form Details:`)
            forms.forEach(form => {
              const moveCount = form.moves?.length || 0
              const statCount = form.stats ? Object.keys(form.stats).length : 0
              console.log(`       - ${form.name}: ${moveCount} moves, ${statCount} stats`)
            })
          }

        } else {
          stats.failedScrapes++
          stats.errors.push(`Failed to scrape ${fruitName}: No fruit data returned`)
          console.log(`  ❌ Failed: ${fruitName} (no fruit data)`)
        }

      } catch (error) {
        stats.failedScrapes++
        const errorMsg = `Failed to scrape ${fruitName}: ${error}`
        stats.errors.push(errorMsg)
        console.log(`  ❌ Error: ${fruitName} - ${error}`)
      }

      // Rate limiting (respectful scraping)
      if (i < members.length - 1) {
        console.log("  ⏸️ Rate limiting pause...")
        await new Promise(resolve => setTimeout(resolve, 500))
      }
    }

    // Calculate final statistics
    if (stats.successfulScrapes > 0) {
      const totalForms = scrapedFruits.reduce((total, fruit) => 
        total + (fruit.fruitData?.forms?.length || 0), 0)
      const totalMoves = scrapedFruits.reduce((total, fruit) => 
        total + (fruit.fruitData?.forms?.reduce((formTotal, form) => 
          formTotal + (form.moves?.length || 0), 0) || 0), 0)

      stats.averageFormsPerFruit = totalForms / stats.successfulScrapes
      stats.averageMovesPerForm = totalForms > 0 ? totalMoves / totalForms : 0
    }

    // Save to database
    if (scrapedFruits.length > 0) {
      console.log(`\n💾 Saving ${scrapedFruits.length} fruits to database...`)
      await collection.insertMany(scrapedFruits)
      console.log(`✅ Successfully saved all fruits to MongoDB`)
    }

    stats.duration = Date.now() - startTime

    // Display final statistics
    displayFinalStats(stats)

    // Save scraping stats
    await saveScrapingStats(db, stats, scrapedFruits)

  } catch (error) {
    console.error("❌ Fatal error during fruit scraping:", error)
    stats.errors.push(`Fatal error: ${error}`)
  } finally {
    await client.close()
  }
}

function displayFinalStats(stats: FruitScrapingStats) {
  console.log("\n" + "=".repeat(60))
  console.log("📊 FRUIT SCRAPING RESULTS")
  console.log("=".repeat(60))

  console.log(`⏱️  Duration: ${Math.round(stats.duration / 1000)}s`)
  console.log(`📦 Attempted: ${stats.totalAttempts} fruits`)
  console.log(`✅ Successful: ${stats.successfulScrapes} fruits`)
  console.log(`❌ Failed: ${stats.failedScrapes} fruits`)
  console.log(`📈 Success Rate: ${Math.round((stats.successfulScrapes / stats.totalAttempts) * 100)}%`)

  console.log(`\n🔍 Data Quality Analysis:`)
  console.log(`  🎭 Fruits with forms: ${stats.fruitsWithForms}/${stats.successfulScrapes}`)
  console.log(`  ⚡ Fruits with awakening: ${stats.fruitsWithAwakening}/${stats.successfulScrapes}`)
  console.log(`  🎯 Fruits with movesets: ${stats.fruitsWithMoveSets}/${stats.successfulScrapes}`)
  console.log(`  📊 Fruits with stats: ${stats.fruitsWithStats}/${stats.successfulScrapes}`)
  console.log(`  📏 Average forms per fruit: ${stats.averageFormsPerFruit.toFixed(1)}`)
  console.log(`  🎲 Average moves per form: ${stats.averageMovesPerForm.toFixed(1)}`)

  if (stats.errors.length > 0) {
    console.log(`\n❌ Errors (${stats.errors.length}):`)
    stats.errors.slice(0, 5).forEach(error => console.log(`  - ${error}`))
    if (stats.errors.length > 5) {
      console.log(`  ... and ${stats.errors.length - 5} more errors`)
    }
  }

  console.log(`\n🚀 Enhanced Features Used:`)
  console.log(`  • Dual tabber format support (<tabber> and {{#tag:tabber|}})`)
  console.log(`  • Robust form extraction with validation`)
  console.log(`  • Enhanced stats table parsing`)
  console.log(`  • Improved recommendation filtering`)
  console.log(`  • Complete mastery requirements extraction`)
}

async function saveScrapingStats(db: any, stats: FruitScrapingStats, fruits: any[]) {
  try {
    const collection = db.collection("scraping_stats")
    await collection.insertOne({
      ...stats,
      timestamp: new Date(),
      version: "specialized-fruit-scraper",
      architecture: "enhanced-fruit-scraper",
      enhancementsUsed: [
        "dual-tabber-format-support",
        "form-validation",
        "enhanced-stats-parsing",
        "recommendation-filtering",
        "mastery-extraction"
      ],
      sampleFruits: fruits.slice(0, 3).map(fruit => ({
        name: fruit.name,
        type: fruit.fruitData?.type,
        formsCount: fruit.fruitData?.forms?.length || 0,
        totalMoves: fruit.fruitData?.forms?.reduce((total: number, form: any) => total + (form.moves?.length || 0), 0) || 0
      }))
    })
    console.log("💾 Scraping statistics saved to database")
  } catch (error) {
    console.error("Failed to save scraping stats:", error)
  }
}

// Handle graceful shutdown
process.on("SIGINT", () => {
  console.log("\n⏹️ Fruit scraping interrupted by user")
  console.log("🔄 You can run this script again to resume scraping")
  process.exit(0)
})

scrapeAllFruits().catch(console.error)