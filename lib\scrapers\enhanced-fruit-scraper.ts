/**
 * Enhanced Fruit Scraper with improved data extraction capabilities
 * Handles complex wikitext patterns and missing data recovery
 */

import { BaseScraper } from "./base-scraper"
import { ScrapedItem, FruitData } from "./types"

export class EnhancedFruitScraper extends BaseScraper {
  
  async scrapeItem(title: string, itemType: string = "fruit"): Promise<ScrapedItem | null> {
    console.log(`🍎 Scraping enhanced fruit data for: ${title}`)
    
    const wikitext = await this.getPageContent(title)
    if (!wikitext) {
      console.error(`❌ No content found for ${title}`)
      return null
    }

    const infoboxData = this.extractTemplateData(wikitext, "Blox Fruit Infobox")
    const fruitData = this.extractEnhancedFruitData(wikitext, infoboxData)
    
    const scrapedItem: ScrapedItem = {
      name: title,
      type: "fruit",
      category: "fruit",
      description: fruitData.mainDescription,
      imageUrl: this.extractMainImageUrl(wikitext, infoboxData),
      imageUrls: this.extractImageUrls(wikitext),
      wikiUrl: `https://blox-fruits.fandom.com/wiki/${encodeURIComponent(title)}`,
      lastUpdated: new Date(),
      fruitData,
      rawData: {
        infobox: infoboxData,
        wikitextLength: wikitext.length,
        movesFound: this.countMoves(wikitext),
        statsFound: this.countStats(wikitext),
        extractedAt: new Date().toISOString()
      }
    }

    return scrapedItem
  }

  extractSpecificData(wikitext: string, infoboxData: Record<string, string>): FruitData {
    return this.extractEnhancedFruitData(wikitext, infoboxData)
  }

  private extractEnhancedFruitData(wikitext: string, infoboxData: Record<string, string>): FruitData {
    console.log("🔍 Extracting enhanced fruit data...")
    
    // Base fruit data from infobox
    const baseFruitData = this.extractBaseFruitData(wikitext, infoboxData)
    
    // Enhanced data extraction
    const statsTableData = this.extractStatsTableData(wikitext);
    const enhancedData = this.extractEnhancedStatsData(wikitext)

    // Specific fruit mechanics
    const furyMeterMechanics = this.extractFuryMeterMechanics(wikitext)
    const passiveAbilities = this.extractPassiveAbilities(wikitext)
    const masteryRequirements = this.extractMasteryRequirements(wikitext)

    // Price and economic data
    const priceData = this.extractPriceData(wikitext, infoboxData)
    const economicData = this.extractEconomicData(wikitext)

    // Transformation and variant data
    const forms = this.extractFormsData(enhancedData.skillBoxData, statsTableData)
    const variantsComplete = enhancedData.variantData
    const variants = this.extractVariants(wikitext);

    // Additional data
    const trivia = this.extractTrivia(wikitext)
    const gallery = this.extractGalleryData(wikitext)
    const changeHistory = this.extractChangeHistory(wikitext)
    const skinSystem = this.extractSkinSystem(wikitext)
    const reworkDetails = this.extractReworkDetails(wikitext);

    // Shop quote
    const shopQuote = this.extractShopQuote(wikitext)

    // Fruit-specific data (Gravity upgrades, instinct data, etc.)
    const fruitSpecificData: any = {};
    if (enhancedData.upgradeData) {
      fruitSpecificData.upgradeData = enhancedData.upgradeData;
    }
    if (enhancedData.instinctData) {
      fruitSpecificData.instinctData = enhancedData.instinctData;
    }

    return {
      ...baseFruitData,
      furyMeterMechanics,
      damageResistance: enhancedData.damageResistance,
      skinSystem,
      variantsComplete,
      variants,
      reworkDetails,
      economicData,
      changeHistoryAdvanced: changeHistory,
      forms,
      passiveAbilities,
      masteryRequirements,
      priceData,
      trivia,
      gallery,
      shopQuote,
      ...fruitSpecificData,
      // Enhanced overview data
      ...this.processOverviewData(enhancedData.overviewData)
    }
  }

  private extractStatsTableData(wikitext: string): Record<string, any[]> {
    const statsData: Record<string, any[]> = {};
    const tablePattern = /\{\{Stats Table\|Name=([^|]+)[\s\S]*?([^}]+)\}\}/g;
    let match;

    while ((match = tablePattern.exec(wikitext)) !== null) {
      const formName = match[1].trim();
      const tableContent = match[2];
      statsData[formName] = this.parseStatsTable(tableContent);
    }

    return statsData;
  }

  private parseStatsTable(tableContent: string): any[] {
    const moves: any[] = [];
    const rowPattern = /\{\{Stats Table Row\|([^}]+)\}\}/g;
    let rowMatch;

    while ((rowMatch = rowPattern.exec(tableContent)) !== null) {
      const rowData = this.parseTableRow(rowMatch[1]);
      if (rowData) {
        moves.push(rowData);
      }
    }

    return moves;
  }

  private parseTableRow(rowData: string): any | null {
    const data: any = {};
    const parts = rowData.split("|");
    
    for (const part of parts) {
      const [key, value] = part.split("=").map(s => s.trim());
      if (key && value) {
        data[key.toLowerCase()] = this.cleanWikitext(value);
      }
    }

    return Object.keys(data).length > 0 ? data : null;
  }

  private extractReworkDetails(wikitext: string): string {
    const reworkMatch = wikitext.match(/Dragon Token Upon the release of Update 24[\s\S]*?The old version of Dragon was made unobtainable\./);
    return reworkMatch ? reworkMatch[0] : "";
  }

  private extractVariants(wikitext: string): any[] {
    const variants = [];
    const variantPattern = /\{\{Variant\|Name=([^|]+)\|FlightSpeed=([^|]+)\|MountingCapacity=(\d+)\|DamageResistance=([^|]+)\|SpecialFeatures=([^}]+)\}\}/g;
    let match;

    while ((match = variantPattern.exec(wikitext)) !== null) {
      variants.push({
        name: match[1],
        flightSpeed: this.extractFlightSpeed(wikitext, match[1]),
        mountingCapacity: parseInt(match[3]),
        damageResistance: match[4],
        specialFeatures: match[5].split(", ")
      });
    }

    return variants;
  }

  private extractFlightSpeed(wikitext: string, variantName: string): string | null {
    const flightSpeedPattern = new RegExp(`${variantName}.*propelling them forwards faster than their original flying speed`);
    const match = wikitext.match(flightSpeedPattern);
    if (match) {
      return "Faster than original flying speed";
    }
    return null;
  }

  private extractBaseFruitData(wikitext: string, infoboxData: Record<string, string>): Partial<FruitData> {
    return {
      type: infoboxData.type as any,
      rarity: infoboxData.rarity as any,
      introducedUpdate: infoboxData.update,
      m1Capability: infoboxData.m1?.toLowerCase() === 'yes',
      awakening: false, // Most fruits don't have awakening
      transformation: this.hasTransformation(wikitext),
      mainDescription: this.extractMainDescription(wikitext),
      combatRating: this.extractCombatRating(wikitext)
    }
  }

  private extractFuryMeterMechanics(wikitext: string): any {
    const furyMechanics: any = {}
    
    // Look for Fury Meter descriptions
    if (wikitext.includes('Fury Meter')) {
      furyMechanics.orangeMeter = {
        fillMethod: "time-based",
        requiredFor: "hybrid transformation",
        drainTriggers: ["move usage", "taking damage"]
      }
      
      if (wikitext.includes('red Fury Meter') || wikitext.includes('second Fury Meter')) {
        furyMechanics.redMeter = {
          fillMethod: "damage-based",
          requiredFor: "full transformation",
          drainTriggers: ["taking damage"]
        }
      }
    }
    
    return Object.keys(furyMechanics).length > 0 ? furyMechanics : undefined
  }

  private extractPassiveAbilities(wikitext: string): Array<{ name: string; description: string; showcaseUrl?: string }> {
    const passives: Array<{ name: string; description: string; showcaseUrl?: string }> = []
    
    // Pattern pour les sections de passifs
    const passivePattern = /\|\s*([^|]+?)\s*\|\s*([\s\S]*?)(?=\|-|\|})/g
    const passiveSection = wikitext.match(/Passive=([\s\S]*?)(?=\|-\|)/)?.[1]
    
    if (passiveSection) {
      let match
      while ((match = passivePattern.exec(passiveSection)) !== null) {
        const name = this.cleanWikitext(match[1])
        const description = this.cleanWikitext(match[2])
        
        if (name && description && name !== 'Name' && name !== 'Description') {
          passives.push({
            name,
            description,
            showcaseUrl: this.extractShowcaseUrl(match[2])
          })
        }
      }
    }
    
    return passives
  }

  private extractMasteryRequirements(wikitext: string): Record<string, number> {
    const masteryReqs: Record<string, number> = {}
    
    // Extract from SkillBox Mas parameters
    const skillPattern = /\{\{SkillBox[^}]*?\|Move\s*=\s*([^|]+)[^}]*?\|Mas\s*=\s*(\d+)/g
    let match
    
    while ((match = skillPattern.exec(wikitext)) !== null) {
      const moveName = this.cleanWikitext(match[1])
      const mastery = parseInt(match[2])
      
      if (moveName && !isNaN(mastery)) {
        masteryReqs[moveName] = mastery
      }
    }
    
    return masteryReqs
  }

  private extractPriceData(wikitext: string, infoboxData: Record<string, string>): any {
    const priceData: any = {
      current: {},
      sources: ["Blox Fruit Dealer", "Shop"]
    }

    // Current prices from infobox
    if (infoboxData.money) {
      priceData.current.money = this.parseNumber(infoboxData.money)
      priceData.current.status = "AVAILABLE"
    }

    // Enhanced Robux extraction with multiple fallbacks
    if (infoboxData.robux) {
      priceData.current.robux = this.parseNumber(infoboxData.robux)
    } else {
      // Fallback: Search for Robux in wikitext patterns (prioritize main price patterns)
      const robuxPatterns = [
        // Priority 1: Infobox patterns
        /\|robux\s*=\s*([0-9,]+)/g,
        // Priority 2: Main description patterns (costs X or Y from dealer)
        /costs.*?\{\{Money\|[0-9,]+\}\}\s*or\s*\{\{Robux\|([0-9,]+)\}\}/g,
        // Priority 3: General Robux templates (but exclude skin prices)
        /\{\{Robux\|([0-9,]+)\}\}(?!\s*\.\))/g, // Exclude patterns ending with .)
        // Priority 4: Generic robux mentions
        /robux.*?([0-9,]+)/gi
      ];

      for (const pattern of robuxPatterns) {
        const matches = [...wikitext.matchAll(pattern)];
        if (matches.length > 0) {
          // For main price patterns, take the first match
          // For skin patterns, skip values that are likely skin prices (< 3000)
          for (const match of matches) {
            const robuxValue = this.parseNumber(match[1] || match[0]);
            if (robuxValue && robuxValue > 0) {
              // Skip likely skin prices for general patterns
              if (pattern.source.includes('Robux') && robuxValue < 3000) {
                continue;
              }
              priceData.current.robux = robuxValue;
              console.log(`✅ Found Robux price via fallback: ${robuxValue}`);
              break;
            }
          }
          if (priceData.current.robux) break;
        }
      }
    }

    // Historical prices from change history
    const historical = this.extractHistoricalPrices(wikitext)
    if (historical.length > 0) {
      priceData.historical = historical
    }

    // Discounts
    const discounts = this.extractDiscounts(wikitext)
    if (discounts.length > 0) {
      priceData.discounts = discounts
    }

    return priceData
  }

  private extractEconomicData(wikitext: string): any {
    return {
      acquisitionMethods: this.extractAcquisitionMethods(wikitext),
      reworkHistory: this.extractReworkHistory(wikitext),
      competitiveRanking: {
        difficulty: this.extractDifficulty(wikitext),
        grinding: this.extractGrindingRating(wikitext),
        pvp: this.extractPvPRating(wikitext)
      },
      marketAnalysis: {
        pricePosition: this.extractPricePosition(wikitext),
        availability: this.extractAvailability(wikitext),
        tradeValue: this.extractTradeValue(wikitext)
      }
    }
  }

  private extractTradeValue(wikitext: string): string {
    if (wikitext.includes("holds significant trade value")) {
      return "High";
    }
    return "";
  }

  private extractFormsData(skillBoxData: Record<string, any[]>, statsTableData: Record<string, any[]>): any[] {
    const forms: any[] = []
    
    for (const [formName, skills] of Object.entries(skillBoxData)) {
      const form = {
        name: formName,
        type: this.determineFormType(formName),
        moves: skills.map(skill => ({
          key: skill.key || "TAP",
          name: skill.name || skill.move,
          description: skill.description || skill.desc,
          mastery: skill.mastery || skill.mas,
          gif: skill.gif,
          gif1: skill.gif1,
          gif2: skill.gif2,
          // Add stats from stats table if available, and merge effects
          ...this.getStatsForMove(skill.key || skill.name, statsTableData[formName], skill.description || skill.desc, skill)
        })),
        images: {}
      }
      
      forms.push(form)
    }
    
    return forms
  }

  private extractSkinSystem(wikitext: string): any {
    const skinSystem: any = {
      defaultSkin: "Green",
      craftableSkins: [],
      chromaticSkins: [],
      acquisition: {}
    }
    
    // Extract skin information from passive abilities section
    if (wikitext.includes('Skins')) {
      const skinSection = wikitext.match(/'''Skins'''[^}]*?([^}]+)/)?.[1]
      
      if (skinSection) {
        // Extract craftable skins
        const craftableMatch = skinSection.match(/Orange.*?Yellow.*?Blue.*?Red.*?Purple.*?Black/i)
        if (craftableMatch) {
          skinSystem.craftableSkins = ["Orange", "Yellow", "Blue", "Red", "Purple", "Black"]
        }
        
        // Extract chromatic skins
        const chromaticMatch = skinSection.match(/Blood Moon.*?Eclipse.*?Ember.*?Phoenix Sky.*?Violet Night/i)
        if (chromaticMatch) {
          skinSystem.chromaticSkins = ["Blood Moon", "Eclipse", "Ember", "Phoenix Sky", "Violet Night"]
        }
        
        // Extract acquisition methods
        if (skinSection.includes('Barista')) {
          skinSystem.acquisition.crafting = "Barista NPC"
        }
        
        if (skinSection.includes('Robux')) {
          skinSystem.acquisition.purchase = "Robux (permanent required)"
        }
      }
    }
    
    return skinSystem
  }

  private extractChangeHistory(wikitext: string): any[] {
    const changeHistory: any[] = []
    
    const historyPattern = /==Change History==([\s\S]*?)(?===|$)/
    const historyMatch = wikitext.match(historyPattern)
    
    if (historyMatch) {
      const historyContent = historyMatch[1]
      
      // Extract updates
      const updatePattern = /\{\{Update\|(\d+(?:\.\d+)?)\}\}([\s\S]*?)(?=\{\{Update|\}\}$)/g
      let updateMatch
      
      while ((updateMatch = updatePattern.exec(historyContent)) !== null) {
        const updateNumber = updateMatch[1]
        const updateContent = updateMatch[2]
        
        const changes = this.extractUpdateChanges(updateContent)
        
        if (changes.length > 0) {
          changeHistory.push({
            update: updateNumber,
            changes,
            type: this.determineUpdateType(updateContent, changes)
          })
        }
      }
    }
    
    return changeHistory
  }

  // Helper methods
  private hasTransformation(wikitext: string): boolean {
    return wikitext.includes('transform') || wikitext.includes('Imperial Evolution') || wikitext.includes('Fury Meter')
  }

  private extractMainDescription(wikitext: string): string {
    // Extract the first paragraph after the infobox
    const descPattern = /\}\}\s*\{\{Quote[^}]*?\}\}\s*'''[^']*?'''\s+([^{]*?)(?=\n\n|==)/
    const match = wikitext.match(descPattern)
    
    if (match) {
      return this.cleanWikitext(match[1])
    }
    
    return ""
  }

  private extractCombatRating(wikitext: string): any {
    const rating: any = {}
    
    if (wikitext.includes('PvP') || wikitext.includes('PVP')) {
      if (wikitext.includes('excellent') || wikitext.includes('one of the best')) {
        rating.pvp = "Excellent"
      } else if (wikitext.includes('good')) {
        rating.pvp = "Good"
      }
    }
    
    if (wikitext.includes('grinding')) {
      if (wikitext.includes('excellent') || wikitext.includes('one of the best')) {
        rating.grinding = "Excellent"
      } else if (wikitext.includes('good')) {
        rating.grinding = "Good"
      }
    }
    
    return rating
  }

  private extractShopQuote(wikitext: string): string {
    const quotePattern = /\{\{Quote\|([^|]+)\|Shop\}\}/
    const match = wikitext.match(quotePattern)
    
    return match ? this.cleanWikitext(match[1]) : ""
  }

  private processOverviewData(overviewData: Record<string, { pros: string[]; cons: string[] }>): any {
    // Flatten overview data for main pros/cons
    const allPros: string[] = []
    const allCons: string[] = []
    
    // Log for debugging
    console.log(`🔍 Processing overview data with ${Object.keys(overviewData).length} forms`)
    
    for (const [form, data] of Object.entries(overviewData)) {
      console.log(`   Form: ${form} - Pros: ${data.pros.length}, Cons: ${data.cons.length}`)
      allPros.push(...data.pros)
      allCons.push(...data.cons)
    }
    
    const result = {
      pros: [...new Set(allPros)], // Remove duplicates
      cons: [...new Set(allCons)],
      // Keep detailed form-specific data as well
      formSpecific: overviewData
    }
    
    console.log(`✅ Final pros: ${result.pros.length}, cons: ${result.cons.length}`)
    
    return result
  }

  private countMoves(wikitext: string): number {
    return (wikitext.match(/\{\{SkillBox/g) || []).length
  }

  private countStats(wikitext: string): number {
    return (wikitext.match(/\{\{Stats Table Row/g) || []).length
  }

  private extractMainImageUrl(wikitext: string, infoboxData: Record<string, string>): string {
    // Extract from infobox image parameter
    if (infoboxData.image) {
      const imageMatch = infoboxData.image.match(/([^|]+\.(?:png|jpg|gif))/i)
      if (imageMatch) {
        const filename = imageMatch[1].trim()
        return `https://static.wikia.nocookie.net/roblox-blox-piece/images/${encodeURIComponent(filename.replace(/ /g, '_'))}`
      }
    }
    
    return ""
  }

  private extractShowcaseUrl(content: string): string | undefined {
    const fileMatch = content.match(/\[\[File:([^|]+)/)
    if (fileMatch) {
      const filename = fileMatch[1].trim()
      return `https://static.wikia.nocookie.net/roblox-blox-piece/images/${encodeURIComponent(filename.replace(/ /g, '_'))}`
    }
    return undefined
  }

  private determineFormType(formName: string): "Normal" | "Hybrid" | "Transformed" {
    if (formName.toLowerCase().includes('hybrid')) return "Hybrid"
    if (formName.toLowerCase().includes('transformed') || formName.toLowerCase().includes('east') || formName.toLowerCase().includes('west')) return "Transformed"
    return "Normal"
  }

  private getStatsForMove(moveName: string, statsData: any[] = [], moveDescription: string = "", skill?: any): any {
    const moveStats: any = this.extractMoveStats(moveDescription);

    const tableStats = statsData.find(stat => 
      stat.name?.toLowerCase() === moveName.toLowerCase() || 
      stat.key?.toLowerCase() === moveName.toLowerCase()
    );

    if (tableStats) {
      Object.assign(moveStats, tableStats);
    }

    // Merge effects from skill if present and not already set
    if (skill && skill.effects && !moveStats.effects) {
      moveStats.effects = skill.effects;
    }

    return moveStats;
  }

  private extractMoveStats(moveDescription: string): any {
    const stats: any = {};
    const damagePatterns = [/deals (\d+) ticks of burn/, /(\d+) damage/];
    const energyPatterns = [/(\d+%?) energy/, /costs (\d+%?) of the user's fury/];

    for (const pattern of damagePatterns) {
      const match = moveDescription.match(pattern);
      if (match) {
        stats.damage = match[1];
        break;
      }
    }

    for (const pattern of energyPatterns) {
      const match = moveDescription.match(pattern);
      if (match) {
        stats.energy = match[1];
        break;
      }
    }

    return stats;
  }

  private determineUpdateType(content: string, changes: any[]): string {
    if (content.includes('rework') || changes.some(c => c.description.includes('rework'))) {
      return "major_rework"
    }
    if (content.includes('release') || changes.some(c => c.description.includes('release'))) {
      return "release"
    }
    if (changes.some(c => c.type === 'buff' || c.type === 'nerf')) {
      return "balance"
    }
    return "general"
  }

  private extractUpdateChanges(updateContent: string): any[] {
    const changes: any[] = []
    
    const bulletPattern = /\*\s*([^\n]+)/g
    let match
    
    while ((match = bulletPattern.exec(updateContent)) !== null) {
      const description = this.cleanWikitext(match[1])
      let type = "general"
      
      if (description.toLowerCase().includes('buff')) type = "buff"
      else if (description.toLowerCase().includes('nerf')) type = "nerf"
      else if (description.toLowerCase().includes('add')) type = "addition"
      else if (description.toLowerCase().includes('fix')) type = "fix"
      
      changes.push({ description, type })
    }
    
    return changes
  }

  private extractHistoricalPrices(wikitext: string): any[] {
    const history: any[] = [];
    const priceChangePattern = /price was changed from ([\d,]+) to ([\d,]+)|changed from (\d[\d,.]*)/gi;
    let match;

    while ((match = priceChangePattern.exec(wikitext)) !== null) {
      if (match[1] && match[2]) {
        history.push({
          money: this.parseNumber(match[1]),
          context: `Changed from ${match[1]} to ${match[2]}`
        });
      } else if (match[3]) {
        history.push({
          money: this.parseNumber(match[3]),
          context: `Pre-rework price`
        });
      }
    }
    return history;
  }

  private extractDiscounts(wikitext: string): any[] {
    const discounts: any[] = [];
    const discountPattern = /discount on purchasing the new permanent Dragon \(making it (\d+,\d+) instead of (\d+,\d+)\)/;
    const match = wikitext.match(discountPattern);

    if (match) {
      discounts.push({
        originalPrice: this.parseNumber(match[2]),
        discountedPrice: this.parseNumber(match[1]),
        type: 'robux',
        context: 'Permanent Dragon Token discount'
      });
    }
    return discounts;
  }

  private extractAcquisitionMethods(wikitext: string): any[] {
    const methods: any[] = [];
    const sources = ["Blox Fruit Dealer", "Dragon Egg", "Kitsune Shrine", "Prehistoric Island", "Blox Fruit Gacha", "Shop", "Sea Event"];
    
    sources.forEach(source => {
      if (wikitext.includes(source)) {
        const method: any = { source };
        if (source === "Dragon Egg") {
          method.location = "Prehistoric Island";
          method.chance = "extremely low";
        }
        if (source === "Kitsune Shrine") {
          method.type = "Sea Event";
        }
        methods.push(method);
      }
    });

    return [...new Map(methods.map(item => [item.source, item])).values()];
  }

  private extractReworkHistory(wikitext: string): any {
    const rework: any = {};
    if (wikitext.includes("Dragon Token")) {
      rework.majorUpdate = "Update 24";
      rework.tokenSystem = "Dragon Token conversion";
    }
    const priceIncreaseMatch = wikitext.match(/increased by (\d+\.\d+)%/);
    if (priceIncreaseMatch) {
      rework.priceIncrease = {
        robux: `${priceIncreaseMatch[1]}%`
      };
    }
    return rework;
  }

  private extractDifficulty(wikitext: string): string {
    if (wikitext.match(/most expensive and difficult-to-obtain/)) return "Highest in game";
    return "";
  }

  private extractGrindingRating(wikitext: string): string {
    const match = wikitext.match(/grinding potential becomes almost on the same level as (\w+)/);
    if (match) return `S-tier when transformed`;
    if (wikitext.match(/good for grinding/i)) return "Good";
    return "";
  }

  private extractPvPRating(wikitext: string): string {
    if (wikitext.match(/one of the best.*for PvP/i)) return "S-tier when transformed";
    if (wikitext.match(/abusable fruits for PvP/i)) return "Excellent";
    return "";
  }

  private extractPricePosition(wikitext: string): string {
    if (wikitext.match(/most expensive fruit/i)) return "Most expensive fruit";
    return "";
  }

  private extractAvailability(wikitext: string): string {
    if (wikitext.match(/extremely low chance/i)) return "Extremely rare";
    return "";
  }
}
