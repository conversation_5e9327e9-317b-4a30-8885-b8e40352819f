#!/usr/bin/env node

import { ScraperCoordinator } from "./scraper-coordinator"
import { FruitScraper } from "./scrapers/fruit-scraper"
import { MaterialScraper } from "./scrapers/material-scraper"
import { WeaponScraper } from "./scrapers/weapon-scraper"
import { AccessoryScraper } from "./scrapers/accessory-scraper"
import { NPCScraper } from "./scrapers/npc-scraper"
import { QuestScraper } from "./scrapers/quest-scraper"
import { EnemyScraper } from "./scrapers/enemy-scraper"
import { MechanicScraper } from "./scrapers/mechanic-scraper"
import { EnhancedFruitScraper } from "./enhanced-fruit-scraper"

const MONGO_URL = process.env.MONGODB_URI || "mongodb://localhost:27017"

async function main() {
  const args = process.argv.slice(2)
  const command = args[0]?.toLowerCase()

  console.log("🚀 Blox Fruits Modular Scraper")
  console.log("==============================")

  try {
    switch (command) {
      case "all":
        await scrapeAll()
        break
      case "fruits":
        await scrapeFruits()
        break
      case "test-enhanced":
        await testEnhancedScraper()
        break
      case "materials":
        await scrapeMaterials()
        break
      case "swords":
        await scrapeSwords()
        break
      case "guns":
        await scrapeGuns()
        break
      case "weapons":
        await scrapeWeapons()
        break
      case "accessories":
        await scrapeAccessories()
        break
      case "npcs":
        await scrapeNPCs()
        break
      case "quests":
        await scrapeQuests()
        break
      case "enemies":
      case "raids":
        await scrapeEnemies()
        break
      case "mechanics":
        await scrapeMechanics()
        break
      case "specific":
        const categories = args.slice(1)
        if (categories.length === 0) {
          console.error("❌ Please specify categories to scrape")
          printUsage()
          process.exit(1)
        }
        await scrapeSpecific(categories)
        break
      default:
        console.error(`❌ Unknown command: ${command}`)
        printUsage()
        process.exit(1)
    }
  } catch (error) {
    console.error("❌ Fatal error:", error)
    process.exit(1)
  }
}

async function scrapeAll() {
  const coordinator = new ScraperCoordinator(MONGO_URL)
  await coordinator.scrapeAll()
}

async function scrapeFruits() {
  console.log("🍎 Scraping fruits only...")
  const coordinator = new ScraperCoordinator(MONGO_URL)
  await coordinator.scrapeSpecific(["fruits"])
}

async function scrapeMaterials() {
  console.log("🧱 Scraping materials only...")
  const coordinator = new ScraperCoordinator(MONGO_URL)
  await coordinator.scrapeSpecific(["materials"])
}

async function scrapeSwords() {
  console.log("⚔️ Scraping swords only...")
  const coordinator = new ScraperCoordinator(MONGO_URL)
  await coordinator.scrapeSpecific(["swords"])
}

async function scrapeGuns() {
  console.log("🔫 Scraping guns only...")
  const coordinator = new ScraperCoordinator(MONGO_URL)
  await coordinator.scrapeSpecific(["guns"])
}

async function scrapeWeapons() {
  console.log("⚔️🔫 Scraping all weapons (swords + guns)...")
  const coordinator = new ScraperCoordinator(MONGO_URL)
  await coordinator.scrapeSpecific(["swords", "guns"])
}

async function scrapeAccessories() {
  console.log("💍 Scraping accessories only...")
  const coordinator = new ScraperCoordinator(MONGO_URL)
  await coordinator.scrapeSpecific(["accessories"])
}

async function scrapeNPCs() {
  console.log("👤 Scraping NPCs only...")
  const coordinator = new ScraperCoordinator(MONGO_URL)
  await coordinator.scrapeSpecific(["npcs"])
}

async function scrapeQuests() {
  console.log("📋 Scraping quests only...")
  const coordinator = new ScraperCoordinator(MONGO_URL)
  await coordinator.scrapeSpecific(["quests"])
}

async function scrapeEnemies() {
  console.log("👹 Scraping enemies/raids only...")
  const coordinator = new ScraperCoordinator(MONGO_URL)
  await coordinator.scrapeSpecific(["raids"])
}

async function scrapeMechanics() {
  console.log("⚙️ Scraping mechanics only...")
  const coordinator = new ScraperCoordinator(MONGO_URL)
  await coordinator.scrapeSpecific(["mechanics"])
}

async function scrapeSpecific(categories: string[]) {
  const coordinator = new ScraperCoordinator(MONGO_URL)
  await coordinator.scrapeSpecific(categories)
}

// Individual scraper usage examples
async function runIndividualScrapers() {
  console.log("🔧 Running individual scrapers...")

  // Example: Use FruitScraper directly
  const fruitScraper = new FruitScraper()
  const fruits = await fruitScraper.scrapeCategory("Blox_Fruits")
  console.log(`Scraped ${fruits.length} fruits`)

  // Example: Use MaterialScraper directly
  const materialScraper = new MaterialScraper()
  const materials = await materialScraper.scrapeCategory("Materials")
  console.log(`Scraped ${materials.length} materials`)

  // Example: Use WeaponScraper directly
  const weaponScraper = new WeaponScraper()
  const swords = await weaponScraper.scrapeCategory("Swords", "sword")
  const guns = await weaponScraper.scrapeCategory("Guns", "gun")
  console.log(`Scraped ${swords.length} swords and ${guns.length} guns`)

  // Example: Use AccessoryScraper directly
  const accessoryScraper = new AccessoryScraper()
  const accessories = await accessoryScraper.scrapeCategory("Accessories")
  console.log(`Scraped ${accessories.length} accessories`)

  // Example: Use NPCScraper directly
  const npcScraper = new NPCScraper()
  const npcs = await npcScraper.scrapeCategory("NPCs")
  console.log(`Scraped ${npcs.length} NPCs`)

  // Example: Use QuestScraper directly
  const questScraper = new QuestScraper()
  const quests = await questScraper.scrapeCategory("Quests")
  console.log(`Scraped ${quests.length} quests`)

  // Example: Use EnemyScraper directly
  const enemyScraper = new EnemyScraper()
  const enemies = await enemyScraper.scrapeCategory("Raids")
  console.log(`Scraped ${enemies.length} enemies/raids`)

  // Example: Use MechanicScraper directly
  const mechanicScraper = new MechanicScraper()
  const mechanics = await mechanicScraper.scrapeCategory("Game_Mechanics")
  console.log(`Scraped ${mechanics.length} mechanics`)
}

function printUsage() {
  console.log(`
Usage: npm run scrape:modular <command> [options]

Commands:
  all                    Scrape all categories (fruits, materials, swords, guns, accessories, npcs, quests, raids, mechanics)
  fruits                 Scrape fruits only
  materials              Scrape materials only
  swords                 Scrape swords only
  guns                   Scrape guns only
  weapons                Scrape both swords and guns
  accessories            Scrape accessories only
  npcs                   Scrape NPCs only
  quests                 Scrape quests only
  enemies/raids          Scrape enemies/raids only
  mechanics              Scrape game mechanics only
  specific <categories>  Scrape specific categories

Examples:
  npm run scrape:modular all
  npm run scrape:modular fruits
  npm run scrape:modular accessories
  npm run scrape:modular specific fruits materials
  npm run scrape:modular specific swords guns accessories

Environment Variables:
  MONGODB_URI           MongoDB connection string (default: mongodb://localhost:27017)
`)
}

// Handle command line execution
if (require.main === module) {
  main().catch(error => {
    console.error("❌ Unhandled error:", error)
    process.exit(1)
  })
}

export {
  ScraperCoordinator,
  FruitScraper,
  MaterialScraper,
  WeaponScraper,
  AccessoryScraper,
  NPCScraper,
  QuestScraper,
  EnemyScraper,
  MechanicScraper
}

async function testEnhancedScraper() {
  console.log("🧪 Testing enhanced scraper improvements...")

  const scraper = new EnhancedFruitScraper()

  try {
    // Test Dragon
    console.log('\n=== Testing Dragon ===')
    const dragonData = await scraper.scrapeItem('Dragon', 'fruit')
    if (dragonData) {
      console.log('✅ Dragon scraped successfully')
      console.log('- Robux price:', dragonData.fruitData?.priceData?.current?.robux)
      console.log('- Money price:', dragonData.fruitData?.priceData?.current?.money)
      console.log('- Upgrade data:', dragonData.fruitData?.upgradeData ? 'Present' : 'Missing')
      console.log('- Instinct data:', dragonData.fruitData?.instinctData ? 'Present' : 'Missing')
      console.log('- Forms count:', dragonData.fruitData?.forms?.length || 0)
      console.log('- Passive abilities:', dragonData.fruitData?.passiveAbilities?.length || 0)
    } else {
      console.log('❌ Dragon scraping failed')
    }

    // Test Gravity
    console.log('\n=== Testing Gravity ===')
    const gravityData = await scraper.scrapeItem('Gravity', 'fruit')
    if (gravityData) {
      console.log('✅ Gravity scraped successfully')
      console.log('- Robux price:', gravityData.fruitData?.priceData?.current?.robux)
      console.log('- Money price:', gravityData.fruitData?.priceData?.current?.money)
      console.log('- Upgrade data:', gravityData.fruitData?.upgradeData ?
        Object.keys(gravityData.fruitData.upgradeData).length + ' entries' : 'Missing')
      console.log('- Instinct data:', gravityData.fruitData?.instinctData ?
        Object.keys(gravityData.fruitData.instinctData).length + ' entries' : 'Missing')
      console.log('- Forms count:', gravityData.fruitData?.forms?.length || 0)
      console.log('- Passive abilities:', gravityData.fruitData?.passiveAbilities?.length || 0)
    } else {
      console.log('❌ Gravity scraping failed')
    }

  } catch (error) {
    console.error('❌ Test failed:', error instanceof Error ? error.message : String(error))
  }
}
