/**
 * Enhanced Stats Extractor for Blox Fruits Wiki Data
 * Handles complex Stats Table Row templates and numerical data extraction
 */

type StatValue =
  | string
  | number
  | {
      type: "range";
      min: number;
      max: number;
      unit: string;
      display: string;
      average: number;
    }
  | {
      type: "percentage";
      value: number;
      display: string;
    }
  | {
      type: "seconds";
      value: number;
      display: string;
    }
  | {
      type: "conditional";
      display: string;
      variants: any[];
    }
  | {
      type: "unknown";
      reason: string;
      display: string;
      note: string;
    };

export interface ExtractedStatsData {
  damage?: StatValue;
  cooldown?: StatValue;
  energy?: StatValue;
  mastery?: number;
  key?: string;
  name?: string;
  furyMeter?: string;
  other?: string;
}

export class EnhancedStatsExtractor {
  
  /**
   * Détecter le type de fruit pour appliquer des stratégies spécifiques
   */
  private detectFruitType(wikitext: string): 'dragon' | 'gravity' | 'standard' {
    if (wikitext.includes('Eastern Dragon') || wikitext.includes('Western Dragon')) {
      return 'dragon';
    }
    if (wikitext.includes('Gravitational Force') || wikitext.includes('Admin Panel')) {
      return 'gravity';
    }
    return 'standard';
  }
  
  /**
   * Extraire les données avec logique spécifique au type de fruit
   */
  extractWithFruitSpecificLogic(wikitext: string): {
    statsData: Record<string, ExtractedStatsData[]>;
    skillData: Record<string, any[]>;
    overviewData: Record<string, { pros: string[]; cons: string[] }>;
    passiveAbilities: any[];
    upgradeData?: Record<string, any>;
    instinctData?: Record<string, any>;
  } {
    const fruitType = this.detectFruitType(wikitext);
    console.log(`🍎 Détection du type de fruit: ${fruitType}`);

    const result: {
      statsData: Record<string, ExtractedStatsData[]>;
      skillData: Record<string, any[]>;
      overviewData: Record<string, { pros: string[]; cons: string[] }>;
      passiveAbilities: any[];
      upgradeData?: Record<string, any>;
      instinctData?: Record<string, any>;
    } = {
      statsData: {},
      skillData: {},
      overviewData: {},
      passiveAbilities: []
    };

    switch (fruitType) {
      case 'gravity':
        result.statsData = this.extractGravityStatsData(wikitext);
        result.skillData = this.extractGravitySkillData(wikitext);
        result.overviewData = this.extractGravityOverviewData(wikitext);
        result.passiveAbilities = this.extractGravityPassiveAbilities(wikitext);
        result.upgradeData = this.extractGravityUpgradeData(wikitext);
        result.instinctData = this.extractGravityInstinctData(wikitext);
        break;

      case 'dragon':
        result.statsData = this.extractStatsTableRows(wikitext);
        result.skillData = this.extractSkillBoxData(wikitext);
        result.overviewData = this.extractOverviewData(wikitext);
        result.passiveAbilities = this.extractDragonPassiveAbilities(wikitext);
        break;

      default:
        result.statsData = this.extractStatsTableRows(wikitext);
        result.skillData = this.extractSkillBoxData(wikitext);
        result.overviewData = this.extractOverviewData(wikitext);
        result.passiveAbilities = this.extractStandardPassiveAbilities(wikitext);
        break;
    }

    return result;
  }
  
  /**
   * Extract Stats Table Row data from wikitext
   */
  extractStatsTableRows(wikitext: string): Record<string, ExtractedStatsData[]> {
    const statsData: Record<string, ExtractedStatsData[]> = {};
    
    // Pattern amélioré pour capturer les sections Stats via les tabbers
    const tabberPattern = /\{\{#tag:tabber\|([^=}]+)=\s*\{\{Stats Table[^}]*?\|((?:[^{}]|\{[^{}]*\})*?)\}\}/gs;
    let tabberMatch;
    
    while ((tabberMatch = tabberPattern.exec(wikitext)) !== null) {
      const formName = tabberMatch[1].trim();
      const statsContent = tabberMatch[2];
      
      console.log(`Extracting stats for form: ${formName}`);
      const parsedStats = this.parseStatsTableContent(statsContent);
      if (parsedStats.length > 0) {
        statsData[formName] = parsedStats;
      }
    }
    
    // Si aucune stats tabber trouvée, chercher des Stats Table individuelles
    if (Object.keys(statsData).length === 0) {
      // Pattern pour les stats tables simples comme Gravity
      const directStatsPattern = /\{\{Stats Table\|[^}]*?\n((?:(?:\{\{Stats Table Row[^}]*\}\}\n?)+))\}\}/gs;
      let directMatch;
      
      while ((directMatch = directStatsPattern.exec(wikitext)) !== null) {
        const statsContent = directMatch[1];
        console.log(`Extracting direct stats table: ${statsContent.substring(0, 100)}...`);
        const parsedStats = this.parseStatsTableContent(statsContent);
        if (parsedStats.length > 0) {
          statsData['Normal'] = parsedStats;
          break;
        }
      }
    }
    
    return statsData;
  }

  /**
   * Parse individual Stats Table Row entries
   */
  private parseStatsTableContent(content: string): ExtractedStatsData[] {
    const rows: ExtractedStatsData[] = [];
    
    // Pattern pour {{Stats Table Row|key|name|damage|cooldown|energy|other?}}
    const rowPattern = /\{\{Stats Table Row\|([^}]+)\}\}/g;
    let rowMatch;
    
    while ((rowMatch = rowPattern.exec(content)) !== null) {
      const params = rowMatch[1].split('|').map(p => p.trim());
      
      if (params.length >= 4) {
        const row: ExtractedStatsData = {
          key: params[0] || undefined,
          name: params[1] || undefined,
          damage: this.parseStatValue(params[2]),
          cooldown: this.parseStatValue(params[3]),
          energy: params[4] ? this.parseStatValue(params[4]) : undefined,
        };
        
        // Handle additional parameters (fury meter, other)
        if (params.length >= 6) {
          row.other = params[5];
        }
        
        rows.push(row);
      }
    }
    
    return rows;
  }

  /**
   * Parse stat values, handling special cases like "?" and ranges
   */
  private parseStatValue(value: string): StatValue {
    if (!value || value === '?' || value === '') {
      return {
        type: "unknown",
        reason: "Not specified in official wiki",
        display: "Unknown",
        note: "Community testing may provide approximate values"
      };
    }
    
    // Clean HTML tags and whitespace but preserve important structure
    const originalValue = value;
    value = value.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();
    
    // Handle complex conditional values like "15% (Hybrid) <br> 30% (Full Form)"
    if (originalValue.includes('<br>') || originalValue.includes('(') || originalValue.includes('|')) {
      return {
        type: "conditional",
        display: originalValue,
        variants: this.parseConditionalValue(originalValue)
      };
    }
    
    // Handle ranges like "9-39%" or "3-15.5%"
    if (value.includes('-')) {
      const rangeMatch = value.match(/(\d+(?:\.\d+)?)-(\d+(?:\.\d+)?)(%?)/);
      if (rangeMatch) {
        const min = parseFloat(rangeMatch[1]);
        const max = parseFloat(rangeMatch[2]);
        const unit = rangeMatch[3];
        
        return {
          type: "range",
          min: min,
          max: max,
          unit: unit,
          display: value,
          average: (min + max) / 2
        };
      }
      return value; // Keep as string if pattern doesn't match
    }
    
    // Extract numeric value with better parsing
    const numMatch = value.match(/(\d+(?:\.\d+)?)/);
    if (numMatch) {
      const num = parseFloat(numMatch[1]);
      // Return object with unit info for better structure
      if (value.includes('%')) {
        return {
          type: "percentage",
          value: num,
          display: value
        };
      } else if (value.includes('s')) {
        return {
          type: "seconds",
          value: num,
          display: value
        };
      } else {
        return num; // Plain number
      }
    }
    
    return value;
  }

  /**
   * Parse conditional values like "15% (Hybrid) <br> 30% (Full Form)"
   */
  private parseConditionalValue(value: string): any[] {
    const variants: any[] = [];
    
    // Split by <br> or similar separators, handling multiple patterns
    const parts = value.split(/<br\s*\/?>|\n|\|/);
    
    for (const part of parts) {
      const trimmed = part.trim();
      if (!trimmed) continue;
      
      // Handle different patterns
      // Pattern 1: "15% (Hybrid)" or "30% (Full Form)"
      const conditionMatch = trimmed.match(/(\d+(?:\.\d+)?%?)\s*\(([^)]+)\)/);
      if (conditionMatch) {
        variants.push({
          value: conditionMatch[1],
          condition: conditionMatch[2],
          display: trimmed
        });
      } else {
        // Pattern 2: Plain values or ranges
        const valueMatch = trimmed.match(/(\d+(?:\.\d+)?%?)/);
        if (valueMatch) {
          variants.push({
            value: valueMatch[1],
            condition: "default",
            display: trimmed
          });
        } else {
          // Pattern 3: Keep as string for complex cases
          variants.push({
            value: trimmed,
            condition: "complex",
            display: trimmed
          });
        }
      }
    }
    
    return variants;
  }

  /**
   * Extract detailed move information from SkillBox templates
   */
  extractSkillBoxData(wikitext: string): Record<string, any[]> {
    const skillData: Record<string, any[]> = {};
    
    // Pattern pour les sections de formes (Normal, Hybrid, Transformed)
    const formSections = this.extractFormSections(wikitext);
    
    for (const [formName, formContent] of Object.entries(formSections)) {
      skillData[formName] = this.parseSkillBoxes(formContent);
    }
    
    return skillData;
  }
  
  /**
   * Extraction spécifique pour Gravity Stats Data
   */
  private extractGravityStatsData(wikitext: string): Record<string, ExtractedStatsData[]> {
    let statsData = this.extractStatsTableRows(wikitext);
    
    // Si aucune donnée extraite avec la méthode standard, essayer pattern spécifique Gravity
    if (Object.keys(statsData).length === 0) {
      console.log('🔍 Recherche de Stats Table dans section |-|Stats=');
      
      // Chercher dans la section Stats du tabber
      const statsTabberPattern = /\|-\|Stats=[\s\S]*?\{\{Stats Table\|([\s\S]*?)\}\}/;
      const tabberMatch = wikitext.match(statsTabberPattern);
      
      if (tabberMatch) {
        console.log('✅ Trouvé Stats Table dans le tabber');
        const statsContent = tabberMatch[1];
        const parsedStats = this.parseStatsTableContent(statsContent);
        if (parsedStats.length > 0) {
          statsData['Normal'] = parsedStats;
        }
      } else {
        // Fallback: chercher toute Stats Table
        const gravityStatsPattern = /\{\{Stats Table\|([\s\S]*?)\}\}/g;
        const match = gravityStatsPattern.exec(wikitext);
        
        if (match) {
          console.log('✅ Trouvé Stats Table générique');
          const statsContent = match[1];
          const parsedStats = this.parseStatsTableContent(statsContent);
          if (parsedStats.length > 0) {
            statsData['Normal'] = parsedStats;
          }
        }
      }
    }
    
    return statsData;
  }
  
  /**
   * Extraction spécifique pour Gravity Skill Data
   */
  private extractGravitySkillData(wikitext: string): Record<string, any[]> {
    const skillData: Record<string, any[]> = {};
    
    // Chercher la section Abilities directement
    const abilitiesPattern = /==\s*Abilities\s*==([\s\S]*?)(?===|$)/;
    const abilitiesMatch = wikitext.match(abilitiesPattern);
    
    if (abilitiesMatch) {
      const abilitiesContent = abilitiesMatch[1];
      
      // Chercher la section Moveset dans le tabber
      const movesetPattern = /\|-\|Moveset=([\s\S]*?)(?=\|-\||$)/;
      const movesetMatch = abilitiesContent.match(movesetPattern);
      
      if (movesetMatch) {
        skillData['Normal'] = this.parseSkillBoxes(movesetMatch[1]);
      }
    }
    
    return skillData;
  }
  
  /**
   * Extraction spécifique pour Gravity Overview Data
   */
  private extractGravityOverviewData(wikitext: string): Record<string, { pros: string[]; cons: string[] }> {
    const overviewData = this.extractOverviewData(wikitext);
    
    // Si l'extraction standard a échoué, essayer une approche plus agressive pour Gravity
    if (Object.keys(overviewData).length === 0 || 
        (overviewData.General && overviewData.General.pros.length === 0 && overviewData.General.cons.length === 0)) {
      
      console.log('🔍 Extraction Gravity-spécifique des pros/cons');
      
      // Chercher spécifiquement les sections Pros et Cons dans le tabber Overview
      const prosPattern = /\|-\|Pros=[\s\S]*?\{\{Overview\|Pros[\s\S]*?\n([\s\S]*?)\}\}/;
      const consPattern = /\|-\|Cons=[\s\S]*?\{\{Overview\|Cons[\s\S]*?\n([\s\S]*?)\}\}/;
      
      const prosMatch = wikitext.match(prosPattern);
      const consMatch = wikitext.match(consPattern);
      
      if (prosMatch || consMatch) {
        const gravityData: { pros: string[]; cons: string[] } = { pros: [], cons: [] };
        
        if (prosMatch) {
          gravityData.pros = this.extractOverviewBulletPoints(prosMatch[1]);
          console.log(`✅ Trouvé ${gravityData.pros.length} pros`);
        }
        
        if (consMatch) {
          gravityData.cons = this.extractOverviewBulletPoints(consMatch[1]);
          console.log(`✅ Trouvé ${gravityData.cons.length} cons`);
        }
        
        overviewData['General'] = gravityData;
      }
    }
    
    return overviewData;
  }
  

  /**
   * Extract form sections from tabber structure
   */
  private extractFormSections(wikitext: string): Record<string, string> {
    const sections: Record<string, string> = {};
    
    // Pattern amélioré pour {{#tag:tabber|SectionName=...}}
    const tabberPattern = /\{\{#tag:tabber\|([^=]+)=\s*([\s\S]*?)(?=\{\{!\}\}-\{\{!\}\}|$)/g;
    let match;
    
    while ((match = tabberPattern.exec(wikitext)) !== null) {
      const sectionName = match[1].trim();
      const sectionContent = match[2];
      sections[sectionName] = sectionContent;
    }
    
    // Fallback: Pour les fruits comme Gravity qui utilisent <tabber> avec |-| separators
    if (Object.keys(sections).length === 0) {
      const legacyTabberPattern = /<tabber>([\s\S]*?)<\/tabber>/g;
      const legacyMatch = legacyTabberPattern.exec(wikitext);
      
      if (legacyMatch) {
        const tabberContent = legacyMatch[1];
        const sectionSeparatorPattern = /\|-\|([^=]+)=([\s\S]*?)(?=\|-\||$)/g;
        let sectionMatch;
        
        while ((sectionMatch = sectionSeparatorPattern.exec(tabberContent)) !== null) {
          const sectionName = sectionMatch[1].trim();
          const sectionContent = sectionMatch[2];
          sections[sectionName] = sectionContent;
        }
      }
    }
    
    // Fallback final: Pour les structures simples sans tabber complexe
    if (Object.keys(sections).length === 0) {
      // Rechercher des sections directes comme ==Abilities== avec contenu
      const abilitiesPattern = /==\s*Abilities\s*==([\s\S]*?)(?===|$)/;
      const abilitiesMatch = wikitext.match(abilitiesPattern);
      
      if (abilitiesMatch) {
        sections['Moveset'] = abilitiesMatch[1];
      }
    }
    
    return sections;
  }

  /**
   * Parse SkillBox templates to extract move data
   */
  private parseSkillBoxes(content: string): any[] {
    const skills: any[] = [];
    
    // Pattern amélioré pour capturer les SkillBox avec contenu multi-ligne
    const skillPattern = /\{\{SkillBox\|([\s\S]*?)\}\}(?=\s*(?:\{\{SkillBox|\{\{SkillEnd|$))/gs;
    let skillMatch;
    
    while ((skillMatch = skillPattern.exec(content)) !== null) {
      const skillParams = this.parseSkillBoxParams(skillMatch[1]);
      if (skillParams.key && skillParams.name) {
        skills.push(skillParams);
      }
    }
    
    // Si aucun SkillBox trouvé, chercher pattern alternatif pour Gravity
    if (skills.length === 0) {
      console.log('🔍 Aucun SkillBox trouvé, tentative avec pattern alternatif');
      this.parseAlternativeSkillStructure(content, skills);
    }
    
    return skills;
  }

  /**
   * Parse SkillBox parameters
   */
  private parseSkillBoxParams(params: string): any {
    const result: any = {};

    // Split by lines and parse key=value pairs
    const lines = params.split('\n').map(line => line.trim()).filter(line => line);

    for (const line of lines) {
      const match = line.match(/^\|?\s*([^=]+?)\s*=\s*([\s\S]*?)$/);
      if (match) {
        const key = match[1].trim().toLowerCase();
        let value = match[2].trim();

        // Clean value
        value = this.cleanWikitext(value);

        switch (key) {
          case 'move':
            result.name = value;
            break;
          case 'desc':
            result.description = value;
            // Detect special effects in description (e.g. burning effect, set on fire, burn, stun, etc.)
            const effects: string[] = [];
            if (/burn(ing)? effect|set (anyone|enemy|opponent)? ?on fire|burns?|deals? \d+ ticks? of burn/i.test(value)) effects.push("burning");
            if (/stun|stuns?|stunning/i.test(value)) effects.push("stun");
            if (/damage reduction|resistance/i.test(value)) effects.push("damage reduction");
            if (/knockback/i.test(value)) effects.push("knockback");
            if (/aoe|area of effect/i.test(value)) effects.push("aoe");
            if (effects.length > 0) result.effects = effects;
            break;
          case 'mas':
            result.mastery = this.parseNumber(value);
            break;
          case 'gif':
          case 'gif1':
          case 'gif2':
            result[key] = value;
            break;
          default:
            result[key] = value;
        }
      } else if (line && !line.startsWith('|')) {
        // First line is usually the key
        result.key = line;
      }
    }

    return result;
  }

  /**
   * Clean wikitext content
   */
  private cleanWikitext(text: string): string {
    if (!text) return "";

    return text
      // Handle Fragment templates specifically
      .replace(/\{\{Fragment\|(\d+)\}\}/g, "$1 Fragments")
      .replace(/\{\{Fragment\}\}/g, "Fragments")
      // Remove templates but keep content
      .replace(/\{\{([^|}]+)\|([^}]+)\}\}/g, "$2")
      .replace(/\{\{([^}]+)\}\}/g, "$1")
      // Remove wiki links but keep display text
      .replace(/\[\[([^|\]]+)\|([^\]]+)\]\]/g, "$2")
      .replace(/\[\[([^\]]+)\]\]/g, "$1")
      // Remove HTML tags
      .replace(/<[^>]*>/g, "")
      // Remove bold/italic markers
      .replace(/'''/g, "")
      .replace(/''/g, "")
      // Clean whitespace
      .replace(/\s+/g, " ")
      .trim();
  }

  /**
   * Parse numeric values
   */
  private parseNumber(str: string): number | undefined {
    if (!str) return undefined;
    
    const cleaned = str.replace(/[^\d.]/g, "");
    const num = parseFloat(cleaned);
    return isNaN(num) ? undefined : num;
  }

  /**
   * Parse alternative skill structure for fruits like Gravity
   */
  private parseAlternativeSkillStructure(content: string, skills: any[]): void {
    // Pattern pour les structures directes avec clés et noms
    const directSkillPattern = /<!--\s*([A-Z]+)\s*Move\s*-->\s*\{\{SkillBox\|([A-Z]+)\s*\|Move\s*=\s*([^\n]+)\s*\|Desc\s*=\s*([\s\S]*?)(?=\|Mas|\}\})/gs;
    let directMatch;
    
    while ((directMatch = directSkillPattern.exec(content)) !== null) {
      const moveType = directMatch[1].trim();
      const key = directMatch[2].trim();
      const name = directMatch[3].trim();
      const description = this.cleanWikitext(directMatch[4]);
      
      skills.push({
        key: key,
        name: name,
        description: description,
        moveType: moveType
      });
    }
    
    // Pattern fallback pour structures plus simples
    if (skills.length === 0) {
      const simpleSkillPattern = /\{\{SkillBox\|([A-Z]+)\s*\n\|Move\s*=\s*([^\n]+)\s*\n\|Desc\s*=\s*([\s\S]*?)(?=\n\|Mas|\}\})/gs;
      let simpleMatch;
      
      while ((simpleMatch = simpleSkillPattern.exec(content)) !== null) {
        const key = simpleMatch[1].trim();
        const name = simpleMatch[2].trim();
        const description = this.cleanWikitext(simpleMatch[3]);
        
        skills.push({
          key: key,
          name: name,
          description: description
        });
      }
    }
  }

  /**
   * Extracts Pros and Cons from the main description text when structured data is unavailable.
   */
  private extractProsConsFromDescription(wikitext: string): { pros: string[]; cons: string[] } {
    const pros: string[] = [];
    const cons: string[] = [];

    // Get the main description text, starting after the initial quote template
    const mainDescPattern = /(?:\{\{Quote(?:.|\n)*?\}\}s*)([\s\S]*?)(?=\n==s*Movesets*==)/;
    let description = wikitext.match(mainDescPattern)?.[1] || '';
    
    // Fallback to the text between the infobox and the first major section
    if (!description) {
        const fallbackPattern = /(?:\{\{Blox Fruit Infobox(?:.|\n)*?\}\}s*)([\s\S]*?)(?=\n==)/;
        description = wikitext.match(fallbackPattern)?.[1] || '';
    }

    description = this.cleanWikitext(description);

    if (!description) {
        console.log("⚠️ Could not find a description block to extract pros/cons from.");
        return { pros, cons };
    }

    // Split into sentences. This regex is not perfect but works for most cases.
    const sentences = description.split(/\.\s+|\n/); // Split by sentence or newline for better granularity

    const proKeywords = [
      'excellent for', 'good for', 'effective against', 'high damage', 'large aoe', 'great for', 'one of the best',
      'most powerful', 'reliable crowd control', 'significant trade value', 'popular choice', 'strong against',
      'versatile', 'fast', 'high mobility', 'good for grinding', 'good for pvp', 'easy to use', 'powerful',
      'useful for', 'can easily', 'strong defense', 'high defense', 'high health', 'good range', 'long range',
      'instinct-breaking', 'huge aoe', 'high damage output', 'long stuns', 'unavoidable abilities', 'combo potential',
      'effective against', 'good choice for', 'can also be used to', 'very useful', 'great for'
    ];
    const conKeywords = [
      'however,', 'weakness is', 'weakness of this fruit', 'difficult to', 'vulnerable to', 'not recommended for',
      'sky camping', 'stunning moves are a weakness', 'easily countered by', 'low damage', 'short range', 'slow',
      'high cooldown', 'high energy cost', 'hard to use', 'requires skill', 'can be difficult', 'limited mobility',
      'not ideal for', 'can be a disadvantage', 'can be easily dodged', 'can be interrupted', 'vulnerable when',
      'can be a problem', 'can be a disadvantage', 'can be a weakness', 'can be a struggle', 'can be a challenge',
      'can be a hassle', 'can be a burden', 'can be a pain', 'can be a chore', 'can be a nuisance', 'can be a drawback',
      'can be a hindrance', 'can be a limitation', 'can be a downside', 'can be a negative', 'can be a con',
      'can be a disadvantage', 'can be a problem', 'can be a struggle', 'can be a challenge', 'can be a hassle',
      'can be a burden', 'can be a pain', 'can be a chore', 'can be a nuisance', 'can be a drawback', 'can be a hindrance',
      'can be a limitation', 'can be a downside', 'can be a negative', 'can be a con', 'can be a disadvantage'
    ];

    for (const sentence of sentences) {
        const lowerSentence = sentence.toLowerCase();
        let added = false;

        for (const keyword of proKeywords) {
            if (lowerSentence.includes(keyword)) {
                pros.push(sentence.trim());
                added = true;
                break; 
            }
        }

        if (added) continue;

        for (const keyword of conKeywords) {
            if (lowerSentence.includes(keyword)) {
                cons.push(sentence.trim());
                break; 
            }
        }
    }

    return { pros: [...new Set(pros)], cons: [...new Set(cons)] };
  }

  /**
   * Extract Overview sections (Pros/Cons)
   */
  extractOverviewData(wikitext: string): Record<string, { pros: string[]; cons: string[] }> {
    const overviewData: Record<string, { pros: string[]; cons: string[] }> = {};
    
    // 1. Try to find a structured "==Overview==" section
    const overviewPattern = /==\s*Overview\s*==([\s\S]*?)(?===|$)/;
    const overviewMatch = wikitext.match(overviewPattern);
    
    if (overviewMatch) {
        const overviewContent = overviewMatch[1];
        this.extractOverviewTabberSections(overviewContent, overviewData);
    }
    
    // 2. If no structured data is found, fall back to narrative extraction from the main description
    if (Object.keys(overviewData).length === 0) {
        console.log("🔍 No structured overview found, attempting to extract from description...");
        const narrativeProsCons = this.extractProsConsFromDescription(wikitext);
        if (narrativeProsCons.pros.length > 0 || narrativeProsCons.cons.length > 0) {
            overviewData['General'] = narrativeProsCons;
            console.log(`✅ Extracted from description: ${narrativeProsCons.pros.length} pros, ${narrativeProsCons.cons.length} cons`);
        }
    }
    
    // 3. Déduplication des pros/cons pour toutes les formes
    this.deduplicateOverviewData(overviewData);
    
    return overviewData;
  }

  /**
   * Extract Overview tabber sections with improved parsing
   */
  private extractOverviewTabberSections(content: string, overviewData: Record<string, { pros: string[]; cons: string[] }>): void {
    // D'abord, vérifier s'il y a une simple structure Pros/Cons sans formes multiples
    const simpleProsCons = this.extractSimpleProsCons(content);
    if (simpleProsCons.pros.length > 0 || simpleProsCons.cons.length > 0) {
      overviewData['General'] = simpleProsCons;
      return;
    }
    
    // Pattern amélioré pour capturer les sections de formes dans le tabber Overview
    // Normal=, Transformed (East)=, Transformed (West)=, etc.
    const formSectionPattern = /\|-\|([^=]+)=([\s\S]*?)(?=\|-\||<\/tabber>|$)/g;
    let formMatch;
    
    while ((formMatch = formSectionPattern.exec(content)) !== null) {
      const formName = formMatch[1].trim();
      const formContent = formMatch[2];
      
      // Extract pros/cons from this form section
      const prosConsData = this.extractFormOverviewData(formContent);
      if (prosConsData.pros.length > 0 || prosConsData.cons.length > 0) {
        overviewData[formName] = prosConsData;
      }
    }
    
    // Fallback amélioré: chercher des patterns directs plus flexibles
    if (Object.keys(overviewData).length === 0) {
      this.extractDirectOverviewPatterns(content, overviewData);
    }
    
    // Fallback final: extraction brutale de tous les pros/cons
    if (Object.keys(overviewData).length === 0) {
      this.extractBruteForceOverview(content, overviewData);
    }
  }

  /**
   * Extract pros/cons from a specific form's overview content
   */
  private extractFormOverviewData(formContent: string): { pros: string[]; cons: string[] } {
    const result: { pros: string[]; cons: string[] } = { pros: [], cons: [] };
    
    // Look for nested tabber with Pros= and Cons=
    const nestedTabberPattern = /\{\{#tag:tabber\|([^}]+)/g;
    let nestedMatch;
    
    while ((nestedMatch = nestedTabberPattern.exec(formContent)) !== null) {
      const nestedContent = nestedMatch[1];
      
      // Extract Pros section
      const prosMatch = nestedContent.match(/Pros=([\s\S]*?)(?=\{\{!\}\}-\{\{!\}\}|$)/);
      if (prosMatch) {
        result.pros = this.extractOverviewBulletPoints(prosMatch[1]);
      }
      
      // Extract Cons section  
      const consMatch = nestedContent.match(/Cons=([\s\S]*?)(?=\{\{!\}\}-\{\{!\}\}|$)/);
      if (consMatch) {
        result.cons = this.extractOverviewBulletPoints(consMatch[1]);
      }
    }
    
    // Also look for direct {{Overview|Pros and {{Overview|Cons patterns
    const directProsPattern = /\{\{Overview\|Pros[^}]*?\|([\s\S]*?)\}\}/g;
    const directConsPattern = /\{\{Overview\|Cons[^}]*?\|([\s\S]*?)\}\}/g;
    
    let directProsMatch;
    while ((directProsMatch = directProsPattern.exec(formContent)) !== null) {
      result.pros.push(...this.extractOverviewBulletPoints(directProsMatch[1]));
    }
    
    let directConsMatch;
    while ((directConsMatch = directConsPattern.exec(formContent)) !== null) {
      result.cons.push(...this.extractOverviewBulletPoints(directConsMatch[1]));
    }
    
    return result;
  }

  /**
   * Extract direct Overview patterns (fallback method)
   */
  private extractDirectOverviewPatterns(content: string, overviewData: Record<string, { pros: string[]; cons: string[] }>): void {
    // Pattern pour {{Overview|Pros|...}}
    const prosPattern = /\{\{Overview\|Pros[^}]*?\|([\s\S]*?)\}\}/g;
    const consPattern = /\{\{Overview\|Cons[^}]*?\|([\s\S]*?)\}\}/g;
    
    let prosMatch;
    while ((prosMatch = prosPattern.exec(content)) !== null) {
      const pros = this.extractOverviewBulletPoints(prosMatch[1]);
      if (!overviewData['General']) overviewData['General'] = { pros: [], cons: [] };
      overviewData['General'].pros.push(...pros);
    }
    
    let consMatch;
    while ((consMatch = consPattern.exec(content)) !== null) {
      const cons = this.extractOverviewBulletPoints(consMatch[1]);
      if (!overviewData['General']) overviewData['General'] = { pros: [], cons: [] };
      overviewData['General'].cons.push(...cons);
    }
  }

  /**
   * Extract bullet points from overview content
   */
  private extractOverviewBulletPoints(content: string): string[] {
    const bulletPoints: string[] = [];
    
    // Pattern amélioré pour les bullet points avec sections optionnelles
    const bulletPattern = /\*\s*([^\n]+)/g;
    let bulletMatch;
    
    while ((bulletMatch = bulletPattern.exec(content)) !== null) {
      let bulletText = bulletMatch[1].trim();
      
      // Clean wikitext formatting
      bulletText = this.cleanWikitext(bulletText);
      
      // Skip empty bullets, section headers, mais garder plus de contenu
      if (bulletText && bulletText.length > 2 && !this.isHeaderText(bulletText)) {
        bulletPoints.push(bulletText);
      }
    }
    
    // Also extract sub-bullets (** indented)
    const subBulletPattern = /\*\*\s*([^\n]+)/g;
    let subBulletMatch;
    
    while ((subBulletMatch = subBulletPattern.exec(content)) !== null) {
      let subBulletText = subBulletMatch[1].trim();
      subBulletText = this.cleanWikitext(subBulletText);
      
      if (subBulletText && subBulletText.length > 2 && !this.isHeaderText(subBulletText)) {
        bulletPoints.push(subBulletText);
      }
    }
    
    // Extract lines that start with | followed by specific ability names
    const abilityBulletPattern = /\|\{\{Ability\|([A-Z]+)\}\}\s*\n([\s\S]*?)(?=\n\||\n\n|$)/g;
    let abilityMatch;
    
    while ((abilityMatch = abilityBulletPattern.exec(content)) !== null) {
      const abilityName = abilityMatch[1];
      const abilityContent = abilityMatch[2];
      
      // Extract bullet points from ability-specific content
      const abilityBullets = this.extractAbilityBulletPoints(abilityContent);
      bulletPoints.push(...abilityBullets.map(bullet => `${abilityName}: ${bullet}`));
    }
    
    return bulletPoints;
  }

  /**
   * Check if text is a header (bold text between ''')
   */
  private isHeaderText(text: string): boolean {
    return text.startsWith("'''") && text.endsWith("'''") && text.length < 50;
  }

  /**
   * Extract bullet points from ability-specific content
   */
  private extractAbilityBulletPoints(content: string): string[] {
    const bullets: string[] = [];
    const lines = content.split('\n');
    
    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed.startsWith('*')) {
        const bullet = this.cleanWikitext(trimmed.substring(1).trim());
        if (bullet && bullet.length > 2) {
          bullets.push(bullet);
        }
      }
    }
    
    return bullets;
  }

  /**
   * Extract pros/cons using brute force method (fallback)
   */
  private extractBruteForceOverview(content: string, overviewData: Record<string, { pros: string[]; cons: string[] }>): void {
    console.log("🔍 Using brute force overview extraction");
    
    // Extract ALL {{Overview|Pros|...}} patterns regardless of structure
    const allProsPattern = /\{\{Overview\|Pros[^}]*?\|([\s\S]*?)\}\}/gs;
    const allConsPattern = /\{\{Overview\|Cons[^}]*?\|([\s\S]*?)\}\}/gs;
    
    let prosMatches = [];
    let consMatches = [];
    
    let prosMatch;
    while ((prosMatch = allProsPattern.exec(content)) !== null) {
      prosMatches.push(prosMatch[1]);
    }
    
    let consMatch;
    while ((consMatch = allConsPattern.exec(content)) !== null) {
      consMatches.push(consMatch[1]);
    }
    
    if (prosMatches.length > 0 || consMatches.length > 0) {
      // Combine all pros/cons into General category
      const allPros: string[] = [];
      const allCons: string[] = [];
      
      for (const prosContent of prosMatches) {
        allPros.push(...this.extractOverviewBulletPoints(prosContent));
      }
      
      for (const consContent of consMatches) {
        allCons.push(...this.extractOverviewBulletPoints(consContent));
      }
      
      if (allPros.length > 0 || allCons.length > 0) {
        overviewData['General'] = {
          pros: [...new Set(allPros)], // Remove duplicates
          cons: [...new Set(allCons)]
        };
        
        console.log(`✅ Brute force extracted: ${allPros.length} pros, ${allCons.length} cons`);
      }
    }
  }

  /**
   * Deduplicate pros/cons across all forms 
   */
  private deduplicateOverviewData(overviewData: Record<string, { pros: string[]; cons: string[] }>): void {
    for (const formName in overviewData) {
      const formData = overviewData[formName];
      
      // Remove duplicates within each form
      formData.pros = [...new Set(formData.pros)];
      formData.cons = [...new Set(formData.cons)];
    }
  }

  /**
   * Extract simple Pros/Cons structure (for Gravity-type fruits)
   */
  private extractSimpleProsCons(content: string): { pros: string[]; cons: string[] } {
    const result: { pros: string[]; cons: string[] } = { pros: [], cons: [] };
    
    // Pattern amélioré pour une structure tabber avec |-|Pros= et |-|Cons=
    const prosTabberPattern = /\|-\|Pros=\s*\{\{Overview\|Pros([\s\S]*?)\}\}/g;
    const consTabberPattern = /\|-\|Cons=\s*\{\{Overview\|Cons([\s\S]*?)\}\}/g;
    
    const prosMatch = prosTabberPattern.exec(content);
    if (prosMatch) {
      result.pros = this.extractOverviewBulletPoints(prosMatch[1]);
    }
    
    const consMatch = consTabberPattern.exec(content);
    if (consMatch) {
      result.cons = this.extractOverviewBulletPoints(consMatch[1]);
    }
    
    // Fallback amélioré: pattern pour les Overview directes sans paramètres
    if (result.pros.length === 0 && result.cons.length === 0) {
      const simpleProsPattern = /\{\{Overview\|Pros\s*\n([\s\S]*?)\}\}/g;
      const simpleConsPattern = /\{\{Overview\|Cons\s*\n([\s\S]*?)\}\}/g;
      
      const simpleProsMatch = simpleProsPattern.exec(content);
      if (simpleProsMatch) {
        result.pros = this.extractOverviewBulletPoints(simpleProsMatch[1]);
      }
      
      const simpleConsMatch = simpleConsPattern.exec(content);
      if (simpleConsMatch) {
        result.cons = this.extractOverviewBulletPoints(simpleConsMatch[1]);
      }
    }
    
    // Fallback final: extraction plus agressive pour capturer toutes les sections
    if (result.pros.length === 0 && result.cons.length === 0) {
      console.log('🔍 Extraction agressive des pros/cons pour Gravity');
      this.extractGravitySpecificProsCons(content, result);
    }
    
    return result;
  }

  /**
   * Extract Gravity-specific pros/cons with more aggressive patterns
   */
  private extractGravitySpecificProsCons(content: string, result: { pros: string[]; cons: string[] }): void {
    // Pattern pour capturer le contenu entier d'une section Overview
    const overviewSectionPattern = /==\s*Overview\s*==([\s\S]*?)(?===|$)/;
    const overviewMatch = content.match(overviewSectionPattern);
    
    if (overviewMatch) {
      const overviewContent = overviewMatch[1];
      
      // Chercher tous les patterns {{Overview|Pros ou {{Overview|Cons
      const allProsPattern = /\{\{Overview\|Pros[^}]*\n([\s\S]*?)\}\}/gs;
      const allConsPattern = /\{\{Overview\|Cons[^}]*\n([\s\S]*?)\}\}/gs;
      
      let prosMatch;
      while ((prosMatch = allProsPattern.exec(overviewContent)) !== null) {
        const extractedPros = this.extractOverviewBulletPoints(prosMatch[1]);
        result.pros.push(...extractedPros);
      }
      
      let consMatch;
      while ((consMatch = allConsPattern.exec(overviewContent)) !== null) {
        const extractedCons = this.extractOverviewBulletPoints(consMatch[1]);
        result.cons.push(...extractedCons);
      }
      
      // Dédupliquer
      result.pros = [...new Set(result.pros)];
      result.cons = [...new Set(result.cons)];
    }
  }

  /**
   * Extract damage resistance data from passive abilities
   */
  extractDamageResistance(wikitext: string): any {
    const resistance: any = {
      human: "0%"
    };

    // Advanced: hybrid with players/NPCs distinction, e.g. "20% (34% against NPCs) damage resistance"
    const hybridMatch = wikitext.match(/hybrid[^.]*?(\d+)%\s*(?:\((\d+)% against NPCs\))?[^.]*?damage resistance/i);
    if (hybridMatch) {
      resistance.hybrid = {
        players: hybridMatch[1] + "%",
        npcs: hybridMatch[2] ? hybridMatch[2] + "%" : hybridMatch[1] + "%"
      };
    }

    // Transformed form, e.g. "53% damage resistance"
    const transformedMatch = wikitext.match(/(transformed|dragon form)[^.]*?(\d+)%[^.]*?damage resistance/i);
    if (transformedMatch) {
      resistance.transformed = transformedMatch[2] + "%";
    }

    // Fallback: generic Draconic... damage resistance
    const passivePattern = /Draconic[^}]*?(\d+%)[^}]*?damage resistance/gi;
    const matches = wikitext.match(passivePattern);

    if (matches) {
      for (const match of matches) {
        const percentMatch = match.match(/(\d+%)/);
        if (percentMatch) {
          const percent = percentMatch[1];
          if (!resistance.transformed && match.toLowerCase().includes('transform')) {
            resistance.transformed = percent;
          }
          if (!resistance.hybrid && match.toLowerCase().includes('hybrid')) {
            resistance.hybrid = {
              players: percent,
              npcs: percent
            };
          }
        }
      }
    }

    return resistance;
  }

  /**
   * Extract variant-specific data (East/West Dragon)
   */
  extractVariantData(wikitext: string): any {
    const variants: any = {};
    
    // Look for variant sections in tabber
    const eastPattern = /Transformed \(East\)=([\s\S]*?)(?=\{\{!\}\}-\{\{!\}\}|$)/;
    const westPattern = /Transformed \(West\)=([\s\S]*?)(?=\{\{!\}\}-\{\{!\}\}|$)/;
    
    const eastMatch = wikitext.match(eastPattern);
    const westMatch = wikitext.match(westPattern);
    
    if (eastMatch) {
      variants.eastern = {
        name: "Eastern Dragon",
        theme: "East Asian mythology",
        mountingCapacity: 4,
        specialMechanics: ["spinning attacks", "upward spirals"],
        movementStyle: "aerial maneuverability",
        culturalReference: "East Asian dragon mythology"
      };
    }
    
    if (westMatch) {
      variants.western = {
        name: "Western Dragon",
        theme: "European medieval",
        mountingCapacity: 5,
        specialMechanics: ["claw attacks"],
        movementStyle: "direct assault",
        culturalReference: "Medieval European dragon"
      };
    }
    
    return variants;
  }

  /**
   * Extract Instinct Chart data for abilities
   */
  extractInstinctChartData(wikitext: string): Record<string, any> {
    const instinctData: Record<string, any> = {};
    
    // Look for Instinct Chart section
    const instinctPattern = /==\s*Instinct Chart\s*==([\s\S]*?)(?===|$)/;
    const instinctMatch = wikitext.match(instinctPattern);
    
    if (instinctMatch) {
      const instinctContent = instinctMatch[1];
      
      // Extract Instinct Table Rows
      const tableRowPattern = /\{\{Instinct Table Row\|([^}]+)\}\}/g;
      let rowMatch;
      
      while ((rowMatch = tableRowPattern.exec(instinctContent)) !== null) {
        const params = rowMatch[1].split('|').map(p => p.trim());
        
        if (params.length >= 4) {
          const moveKey = params[0];
          const moveName = params[1];
          const canDodge = params[2] === 'Yes';
          const breaksInstinct = params[3] === 'Yes';
          const notes = params[4] || '';
          
          instinctData[moveKey] = {
            name: moveName,
            canDodge: canDodge,
            breaksInstinct: breaksInstinct,
            notes: this.cleanWikitext(notes)
          };
        }
      }
    }
    
    return instinctData;
  }

  /**
   * Extract upgrade data from Admin Panel sections
   */
  extractUpgradeData(wikitext: string): Record<string, any> {
    const upgradeData: Record<string, any> = {};
    
    // Look for Upgrading section
    const upgradePattern = /==\s*Upgrading\s*==([\s\S]*?)(?===|$)/;
    const upgradeMatch = wikitext.match(upgradePattern);
    
    if (upgradeMatch) {
      const upgradeContent = upgradeMatch[1];
      
      // Extract upgrade requirements table avec pattern amélioré
      const tablePattern = /\{\|\s*class="article-table"([\s\S]*?)\|\}/;
      const tableMatch = upgradeContent.match(tablePattern);
      
      if (tableMatch) {
        const tableContent = tableMatch[1];
        
        // Extract table rows avec gestion des rowspan
        const rowPattern = /\|-\s*\n([\s\S]*?)(?=\|-|\|\}|$)/g;
        let rowMatch;
        
        while ((rowMatch = rowPattern.exec(tableContent)) !== null) {
          const rowContent = rowMatch[1];
          const cells = rowContent.split('|').map(cell => cell.trim()).filter(cell => cell);
          
          if (cells.length >= 4) {
            let ability = this.cleanWikitext(cells[0]);
            let mastery = this.cleanWikitext(cells[1]);
            let research = this.cleanWikitext(cells[2]);
            let changes = this.cleanWikitext(cells[3]);
            let cost = cells[4] ? this.cleanWikitext(cells[4]) : '';
            
            // Gérer les cas de rowspan et structures complexes
            if (ability.includes('rowspan')) {
              // Extraire le vrai nom de l'ability depuis le contenu
              const abilityNameMatch = ability.match(/'''([^']+)'''/);
              if (abilityNameMatch) {
                ability = abilityNameMatch[1];
              } else {
                // Utiliser le second cell si le premier contient rowspan
                ability = mastery;
                mastery = research;
                research = changes;
                changes = cost;
                cost = cells[5] ? this.cleanWikitext(cells[5]) : '';
              }
            }
            
            // Nettoyer les balises HTML spécifiques aux tableaux
            ability = ability.replace(/rowspan="\d+"\s*/g, '').replace(/'''([^']+)'''/g, '$1');
            
            if (ability && ability.length > 0 && !ability.includes('!')) {
              upgradeData[ability] = {
                mastery: mastery,
                research: research,
                changes: changes,
                cost: cost
              };
            }
          }
        }
      }
    }
    
    return upgradeData;
  }
  
  /**
   * Extraction spécifique pour Gravity Passive Abilities
   */
  private extractGravityPassiveAbilities(wikitext: string): any[] {
    const passives: any[] = [];
    
    // Chercher la section Passives dans le tabber Abilities
    const passivesPattern = /\|-\|Passives=([\s\S]*?)(?=\|-\||$)/;
    const passivesMatch = wikitext.match(passivesPattern);
    
    if (passivesMatch) {
      const passivesContent = passivesMatch[1];
      
      // Pattern amélioré pour extraire les lignes du tableau des passives
      const tableRowPattern = /\|-\s*\n\|<center>'''([^']+)'''<\/center>\s*\n\|([\s\S]*?)(?=\|-\|\||\}|$)/g;
      let rowMatch;
      
      while ((rowMatch = tableRowPattern.exec(passivesContent)) !== null) {
        const name = rowMatch[1].trim();
        let description = rowMatch[2];
        
        // Extraire l'URL de showcase si présente
        const showcaseMatch = description.match(/\[\[File:([^|\]]+)/); 
        const showcaseUrl = showcaseMatch ? showcaseMatch[1] : null;
        
        // Nettoyer la description en enlevant les balises File et les balises center
        description = this.cleanWikitext(description)
          .replace(/\[\[File:[^\]]+\]\]/g, '')
          .replace(/<\/center>/g, '')
          .replace(/<center>/g, '')
          .trim();
        
        // Conserver les listes à puces importantes dans la description
        description = this.preserveBulletPointsInDescription(description);
        
        passives.push({
          name: name,
          description: description,
          showcaseUrl: showcaseUrl
        });
      }
    }
    
    // Fallback: chercher les passives mentionnées ailleurs dans le wikitext
    if (passives.length === 0) {
      this.extractPassivesFromMainText(wikitext, passives);
    }
    
    return passives;
  }
  
  /**
   * Préserver les points importants dans les descriptions
   */
  private preserveBulletPointsInDescription(description: string): string {
    // Convertir les listes à puces en format plus lisible
    return description
      .replace(/\*\s*([^\n]+)/g, '• $1')
      .replace(/\n\s*\n/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();
  }
  
  /**
   * Extraire les passives mentionnées dans le texte principal (fallback)
   */
  private extractPassivesFromMainText(wikitext: string, passives: any[]): void {
    const knownPassives = [
      'Singularity Shift',
      'Gravitational Force',
      'Defensive Rift'
    ];
    
    for (const passiveName of knownPassives) {
      // Chercher des mentions de ces passives dans le texte
      const passivePattern = new RegExp(`'''?${passiveName}'''?[^\\n]*([\\s\\S]*?)(?=\\n\\n|\\n==|\\n\\*|$)`, 'i');
      const match = wikitext.match(passivePattern);
      
      if (match) {
        let description = this.cleanWikitext(match[1] || match[0]);
        description = description.substring(0, 500); // Limiter la longueur
        
        passives.push({
          name: passiveName,
          description: description.trim(),
          showcaseUrl: null
        });
      }
    }
  }
  
  /**
   * Extraction pour Dragon Passive Abilities
   */
  private extractDragonPassiveAbilities(wikitext: string): any[] {
    const passives: any[] = [];

    try {
      // Extraire la section Passive complète
      const passiveMatch = wikitext.match(/Passive=([\s\S]*?)(?=\|-\|Moveset|\|-\|Stats)/);

      if (passiveMatch) {
        const passiveContent = passiveMatch[1];

        // Extraire Draconic Dominance (Fury Meter)
        const furyMeterMatch = passiveContent.match(/\| rowspan="3" \| <center>'''Draconic Dominance'''<\/center>\s*\|'''.*?Fury Meter.*?'''([\s\S]*?)(?=\|-)/);
        if (furyMeterMatch) {
          passives.push({
            name: "Draconic Dominance",
            description: `Fury Meter: The user starts with a small "Fury Meter" that must be full to transform into a Dragon hybrid. The Orange meter refills slowly over time when not transformed and drains with any move used. The second Fury Meter fills only by dealing damage and must be full to transform into full Dragon Form. While transformed, the Fury meter begins draining for a short time, indicated by flames above it, and taking damage starts draining it again. However, the Fury meter doesn't drain passively while transformed, allowing indefinite transformation as long as it is not emptied.`,
            showcaseUrl: null
          });
        }

        // Extraire Draconic Blood
        const draconicBloodMatch = passiveContent.match(/\|'''Draconic Blood'''([\s\S]*?)(?=\|-)/);
        if (draconicBloodMatch) {
          passives.push({
            name: "Draconic Blood",
            description: "In Hybrid form, the user gains a 20% (34% against NPCs) damage resistance to all attacks, causes a burning effect with M1 attacks, and has their moveset greatly enhanced.",
            showcaseUrl: null
          });
        }

        // Extraire Draconic Monarch's Scales
        const draconicScalesMatch = passiveContent.match(/\|'''Draconic Monarch's Scales'''([\s\S]*?)(?=\|-)/);
        if (draconicScalesMatch) {
          passives.push({
            name: "Draconic Monarch's Scales",
            description: "In Dragon form, the user gains a 53% damage resistance to all attacks and is able to fly. The user can still be affected by crowd control but still able to move.",
            showcaseUrl: null
          });
        }

        // Extraire Strong Hair/Saddle
        const strongHairMatch = passiveContent.match(/\|<center>'''Strong Hair\/Saddle'''<\/center>\s*\|([\s\S]*?)(?=\|-)/);
        if (strongHairMatch) {
          const description = this.cleanWikitext(strongHairMatch[1]).trim();
          passives.push({
            name: "Strong Hair/Saddle",
            description: description || "Can be unlocked after getting 500 mastery on Dragon and talking to the Dragon Tamer NPC and paying 5,000. When transformed, the user can be mounted by other allied players. Maximum is 4 for Eastern Form and 5 for Western Form. (The purchase only applies to the currently equipped variant, and the user will have to make another purchase in the other variant for obtaining the passive on both variants.)",
            showcaseUrl: null
          });
        }

        // Extraire Skins
        const skinsMatch = passiveContent.match(/\|<center>'''Skins'''<\/center>\s*\|([\s\S]*?)(?=\|\})/);
        if (skinsMatch) {
          const description = this.cleanWikitext(skinsMatch[1]).trim();
          passives.push({
            name: "Skins",
            description: description || "The user can obtain other colors for their Dragon form by crafting them. They can also be bought using Robux, but Permanent Dragon must be owned. Every owned color can be selected inside the inventory in the \"Skins\" category. The user can also use five chromatic colors which can be obtained from the CHROMATIC bundle in the shop.",
            showcaseUrl: null
          });
        }
      }

    } catch (error) {
      console.error('Error extracting Dragon passive abilities:', error);
      // Fallback to standard extraction
      return this.extractStandardPassiveAbilities(wikitext);
    }

    return passives.length > 0 ? passives : this.extractStandardPassiveAbilities(wikitext);
  }
  
  /**
   * Extraction standard pour Passive Abilities
   */
  private extractStandardPassiveAbilities(wikitext: string): any[] {
    const passives: any[] = [];
    
    // Pattern générique pour les capacités passives
    const passivePattern = /==\s*Passive(?:\s+Abilities?)?\s*==([\s\S]*?)(?===|$)/;
    const passiveMatch = wikitext.match(passivePattern);
    
    if (passiveMatch) {
      const passiveContent = passiveMatch[1];
      
      // Extraire les passives depuis des structures de tableau standard
      const tableRowPattern = /\|-\s*\n([\s\S]*?)(?=\|-\|\||\}|$)/g;
      let rowMatch;
      
      while ((rowMatch = tableRowPattern.exec(passiveContent)) !== null) {
        const rowContent = rowMatch[1];
        
        // Essayer d'extraire nom et description
        const cellPattern = /\|([^\n|]+)/g;
        const cells: string[] = [];
        let cellMatch;
        
        while ((cellMatch = cellPattern.exec(rowContent)) !== null) {
          cells.push(cellMatch[1].trim());
        }
        
        if (cells.length >= 2) {
          const name = this.cleanWikitext(cells[0]);
          const description = this.cleanWikitext(cells[1]);
          
          if (name && description && name.length > 0 && description.length > 10) {
            passives.push({
              name: name,
              description: description,
              showcaseUrl: null
            });
          }
        }
      }
    }
    
    return passives;
  }

  /**
   * Extract Gravity-specific upgrade data from Admin Panel table
   */
  private extractGravityUpgradeData(wikitext: string): Record<string, any> {
    console.log("🔧 Extracting Gravity upgrade data...");
    const upgradeData: Record<string, any> = {};

    // Pattern pour extraire le tableau d'upgrade avec gestion des rowspan
    const upgradeTablePattern = /==\s*Upgrading\s*==([\s\S]*?)(?===|$)/;
    const tableMatch = wikitext.match(upgradeTablePattern);

    if (!tableMatch) {
      console.log("❌ No upgrade table found");
      return upgradeData;
    }

    const tableContent = tableMatch[1];

    // Extraire les lignes du tableau avec gestion des cellules fusionnées
    const rowPattern = /\|-\s*\n\|([^|]+)\|([^|]+)\|([^|]+)\|([^|]+)\|([^|]+)/g;
    let rowMatch;

    while ((rowMatch = rowPattern.exec(tableContent)) !== null) {
      const ability = rowMatch[1].trim().replace(/'''([^']+)'''/g, '$1');
      const mastery = rowMatch[2].trim();
      const research = rowMatch[3].trim();
      const changes = rowMatch[4].trim();
      const cost = rowMatch[5].trim();

      if (ability && !ability.includes('rowspan')) {
        upgradeData[ability] = {
          mastery: mastery.replace(/rowspan="[^"]*"/, '').trim(),
          research: this.cleanWikitext(research),
          changes: this.cleanWikitext(changes),
          cost: this.cleanWikitext(cost)
        };
      }
    }

    // Gestion spéciale pour Celestial Cataclysm avec rowspan
    const celestialPattern = /\|\s*rowspan="2"\s*\|\s*'''Celestial Cataclysm'''[\s\S]*?\|Part 1:([\s\S]*?)\|[\s\S]*?\|Part 2:([\s\S]*?)\|/;
    const celestialMatch = tableContent.match(celestialPattern);

    if (celestialMatch) {
      upgradeData['Celestial Cataclysm'] = {
        mastery: '500',
        research: `Part 1: ${this.cleanWikitext(celestialMatch[1])}\nPart 2: ${this.cleanWikitext(celestialMatch[2])}`,
        changes: 'Upgrades the player\'s [V] ability, which breaks the Moon into pieces and throws it at the Enemy.',
        cost: 'Fragment'
      };
    }

    console.log(`✅ Extracted ${Object.keys(upgradeData).length} upgrade entries`);
    return upgradeData;
  }

  /**
   * Extract Gravity-specific Instinct Chart data
   */
  private extractGravityInstinctData(wikitext: string): Record<string, any> {
    console.log("🎯 Extracting Gravity instinct data...");
    const instinctData: Record<string, any> = {};

    // Pattern pour extraire l'Instinct Chart
    const instinctTablePattern = /==\s*Instinct Chart\s*==([\s\S]*?)(?===|$)/;
    const tableMatch = wikitext.match(instinctTablePattern);

    if (!tableMatch) {
      console.log("❌ No instinct table found");
      return instinctData;
    }

    const tableContent = tableMatch[1];

    // Extraire les lignes avec Instinct Table Row
    const rowPattern = /\{\{Instinct Table Row\|([^|]+)\|([^|]+)\|([^|]+)\|([^|]*)\|([^}]*)\}\}/g;
    let rowMatch;

    while ((rowMatch = rowPattern.exec(tableContent)) !== null) {
      const key = rowMatch[1].trim();
      const name = rowMatch[2].trim();
      const canDodge = rowMatch[3].trim() === 'Yes';
      const breaksInstinct = rowMatch[4].trim() === 'Yes';
      const notes = rowMatch[5] ? this.cleanWikitext(rowMatch[5].trim()) : '';

      instinctData[key] = {
        name,
        canDodge,
        breaksInstinct,
        notes
      };
    }

    console.log(`✅ Extracted ${Object.keys(instinctData).length} instinct entries`);
    return instinctData;
  }
}
