import { EnhancedFruitScraper } from "./enhanced-fruit-scraper";

async function quickTest() {
  console.log('🧪 Final test of enhanced scraper...');

  const scraper = new EnhancedFruitScraper();

  try {
    // Test both Dragon and Gravity
    console.log('\n=== Testing Dragon ===');
    const dragonData = await scraper.scrapeItem('Dragon', 'fruit');
    if (dragonData) {
      console.log('✅ Dragon scraped successfully');
      console.log('- Robux price:', dragonData.fruitData?.priceData?.current?.robux);
      console.log('- Money price:', dragonData.fruitData?.priceData?.current?.money);
      console.log('- Passive abilities:', dragonData.fruitData?.passiveAbilities?.length || 0);
    }

    console.log('\n=== Testing Gravity ===');
    const gravityData = await scraper.scrapeItem('Gravity', 'fruit');
    if (gravityData) {
      console.log('✅ Gravity scraped successfully');
      console.log('- Robux price:', gravityData.fruitData?.priceData?.current?.robux);
      console.log('- Money price:', gravityData.fruitData?.priceData?.current?.money);
      console.log('- Upgrade data:', gravityData.fruitData?.upgradeData ?
        Object.keys(gravityData.fruitData.upgradeData).length + ' entries' : 'Missing');
      console.log('- Instinct data:', gravityData.fruitData?.instinctData ?
        Object.keys(gravityData.fruitData.instinctData).length + ' entries' : 'Missing');
      console.log('- Passive abilities:', gravityData.fruitData?.passiveAbilities?.length || 0);

      // Show passive names
      if (gravityData.fruitData?.passiveAbilities?.length > 0) {
        console.log('\n🔮 Passive Abilities:');
        gravityData.fruitData.passiveAbilities.forEach((passive: any, index: number) => {
          console.log(`  ${index + 1}. ${passive.name}`);
        });
      }
    }

  } catch (error) {
    console.error('❌ Test failed:', error instanceof Error ? error.message : String(error));
  }
}

quickTest();
