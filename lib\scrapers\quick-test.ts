import { EnhancedFruitScraper } from "./enhanced-fruit-scraper";

async function quickTest() {
  console.log('🧪 Quick test of enhanced scraper...');
  
  const scraper = new EnhancedFruitScraper();
  
  try {
    // Test Gravity for upgrade/instinct data
    console.log('\n=== Testing Gravity ===');
    const gravityData = await scraper.scrapeItem('Gravity', 'fruit');
    if (gravityData) {
      console.log('✅ Gravity scraped successfully');
      console.log('- Robux price:', gravityData.fruitData?.priceData?.current?.robux);
      console.log('- Money price:', gravityData.fruitData?.priceData?.current?.money);
      console.log('- Upgrade data:', gravityData.fruitData?.upgradeData ?
        Object.keys(gravityData.fruitData.upgradeData).length + ' entries' : 'Missing');
      console.log('- Instinct data:', gravityData.fruitData?.instinctData ?
        Object.keys(gravityData.fruitData.instinctData).length + ' entries' : 'Missing');
      console.log('- Passive abilities:', gravityData.fruitData?.passiveAbilities?.length || 0);

      // Show upgrade details if present
      if (gravityData.fruitData?.upgradeData) {
        console.log('\n📋 Upgrade Details:');
        for (const [name, data] of Object.entries(gravityData.fruitData.upgradeData)) {
          console.log(`  - ${name}: Mastery ${data.mastery}`);
        }
      }

      // Show instinct details if present
      if (gravityData.fruitData?.instinctData) {
        console.log('\n🎯 Instinct Details:');
        for (const [key, data] of Object.entries(gravityData.fruitData.instinctData)) {
          console.log(`  - ${key}: ${data.name} (Breaks: ${data.breaksInstinct})`);
        }
      }
    } else {
      console.log('❌ Gravity scraping failed');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error instanceof Error ? error.message : String(error));
  }
}

quickTest();
