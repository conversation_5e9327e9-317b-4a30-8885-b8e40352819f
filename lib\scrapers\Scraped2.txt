{"_id": {"$oid": "687550084d7840859668056d"}, "name": "Gravity", "category": "fruit", "fruitData": {"statsData": {"Normal": [{"key": "TAP", "name": "Normal Attack", "damage": 2, "cooldown": 10, "energy": null}, {"key": "Z", "name": "Singularity", "damage": 9, "cooldown": 50, "energy": null}, {"key": "X", "name": "Orbital Chain", "damage": 13, "cooldown": 75, "energy": null}, {"key": "C", "name": "Gravitational Prison", "damage": 16, "cooldown": 90, "energy": null}, {"key": "V", "name": "Asteroid Crash", "damage": 23, "cooldown": 100, "energy": null}, {"key": "F", "name": "Shooting Star", "damage": 5, "cooldown": 25, "energy": null}]}, "overviewData": {"General": {"pros": ["Insane damage.", "High trade value.", "Great combo potential.", "All moves have a huge hitbox.", "All moves break Instinct.", "Can be good for grinding as Ability|C"], "cons": ["Weak in air.", "Cannot catch up with fast fruits/swords users. (for example: Light, Portal, etc.)", "Moves have relatively longer start-up than other PvP fruits, making it easier for enemies to dodge the attacks.", "Requires a certain amount of prediction.", "Passive teleportation completely overrides Draco and Human V4 flash step.", "Has some end-lag.", "High mastery requirement. (Even higher to unlock the upgrades)"]}}, "skillData": {"Normal": [{"key": "On ground, the user summons materials from within their vicinity, forming a spherical orb of different materials upon which they can ride on. In air, the user creates an additional two meteorites that the user then shoots out. When released, this orb is launched in the direction of the user's cursor, dealing minor AoE damage. Additionally, all other Gravity moves can be used while this move is being used.", "name": "Shooting Star", "description": "", "mastery": 50, "gif": "GravFT", "gif1": "GravFHG", "gif2": "GravFHA", "effects": ["stun", "aoe"], "gif3": "GravityFSea"}]}, "passiveAbilities": [{"name": "Singularity Shift", "description": "Whenever the player uses Flash Step, a lingering gravitational effect appears, dealing minor damage if a target comes near the initial teleportation area. This passive is quite similar to Yeti passive but does not last as long.", "showcaseUrl": "GravitationalStepsofForce.gif"}, {"name": "Gravitational Force", "description": "Upon unlocking and equipping the 3rd upgrade from the Admin Panel, Mysterious Scientist, a purple meter called \"Gravitational Force\" will be shown above the user's moveset. The meter fills up only by doing damage with Gra<PERSON>'s moves, and it very slowly depletes when the user is not dealing damage for a few seconds. The meter doesn't affect the damage or range of the moves, but it enables the use of [C] and [V] Ultimate moves when having a certain proportion of the meter filled up. On players, the bar will fill up twice as much when dealing damage to them, possibly because of allowing the user to be able to fill up the meter and use the ultimate within the duration of the fight. *If the user has only unlocked the 3rd upgrade, nothing special will happen when completely filled up. *If the user has unlocked and equipped the 4th upgrade, the bar will light on fire when completely full, similar to Kitsune and <PERSON>'s meter upon transformation.", "showcaseUrl": "GravitationalForce.png"}, {"name": "Defensive Rift", "description": "Using the F move, \"Shooting Star\" will defend the user by making a pulse of gravity that knockbacks and damages nearby enemies whilst simultaneously backing away when they consecutively lose 20% of their HP. Using the F move multiple times before this passive takes effect will increase the amount of pulses made, increasing the damage caused by the knockback. The F move will be put on cooldown once the passive activates.", "showcaseUrl": "GravityDefense.gif"}], "instinctData": {"Tap": {"name": "Normal Attack", "canDodge": true, "breaksInstinct": false, "notes": ""}, "Z": {"name": "Singularity", "canDodge": true, "breaksInstinct": true, "notes": "Only the pull when held can be dodged. The explosion breaks Instinct."}, "X": {"name": "Orbital Chain", "canDodge": true, "breaksInstinct": false, "notes": "Breaks Instinct no matter what."}, "C": {"name": "Gravitational Prison (Held)", "canDodge": true, "breaksInstinct": false, "notes": "Breaks Instinct no matter what."}, "V": {"name": "Asteroid Crash (Held)", "canDodge": true, "breaksInstinct": false, "notes": "Breaks Instinct no matter what."}, "F": {"name": "Shooting Star", "canDodge": true, "breaksInstinct": false, "notes": "Breaks Instinct no matter what."}}, "upgradeData": {"Kinetic Amplifier": {"mastery": "200", "research": "Defeat 15 enemies with the [M1] ability of Gravity. It is recommended to defeat Bandits at Pirate Starter or Trainees at Marine Starter in the First Sea due to their low health.", "changes": "Upgrades [M1] ability, allowing the player to hold their [M1] for more damage and range.", "cost": "Fragment"}, "Kinetic Amplifier 2": {"mastery": "300", "research": "Defeat 30 enemies with the [M1] ability of Gravity. It is recommended to defeat Bandits at Pirate Starter or Trainees at Marine Starter in the First Sea due to their low health.", "changes": "Further upgrades [M1] ability, allowing the player to hold their [M1] for even more damage and range.", "cost": "Fragment"}, "Dark Matter Implosion": {"mastery": "400", "research": "Use Gravity skills on ships 20 times.", "changes": "Grants a Gravitational Force Meter, as well as upgrading the [C] ability, changing its functionality and dealing more damage when held at full Gravitational Force Meter.", "cost": "Fragment"}, "Celestial Cataclysm": {"mastery": "rowspan=\"2\"", "research": "500", "changes": "Part 1: Shoot 20 meteors into water using the [M1] ability of Gravity.", "cost": "rowspan=\"2\""}}, "damageResistance": {"human": "0%"}, "mainDescription": "Gravity is a Mythical Natural-type Blox Fruit that costs 2,500,000 or 2,300 from the Blox Fruit Dealer. Gravity allows the user to control and manipulate the gravitational force in their environment, enabling them to influence many elements on the battlefield. They can create powerful black holes, levitate rocks, manipulate the gravitational force acting on enemies, and pull-down meteors—both large and small—towards the earth. The user can cause island-scale destruction by creating huge meteors and large black holes, even pulling the entire moon down to earth as an attack. Any foes will be easily destroyed by the sheer power of the user. Gravity is widely regarded as one of the most powerful and abusable fruits for PvP due to its high damage output, expansive AoE capabilities, very long stuns, extremely long range and largely unavoidable abilities—especially when fully upgraded—making it an popular choice. Several of its skills, particularly C and V, are very abusable due to their exceptional damage and AoE range, and for <PERSON>, the user is able to become fully invincible to all attacks during the cutscene. As a result, Gravity holds significant trade value, despite being the least expensive among Mythical fruits. It offers excellent combo potential and reliable crowd control, making it particularly effective against many Beast-type fruits such as Buddha, Mammoth , Dragon and even teamers. Gravity is a good choice for grinding in all seas, due to the generally useful [M1] abilities, the huge AoE and highly damaging moves. However, it is not recommended for players in the first sea as the abilities all have extremely high mastery and the [M1] isn't so reliable for consistent damage. Gravity is excellent for all types of activities, from Sea Events to Raids, and even fighting generally shifty enemies such as the Tyrant of the Skies and Order, due to its huge and long stuns that can hold enemies in one place. Gravity can also be used to eliminate dangerous sea events quickly such as Piranhas and Terrorsharks, though the V cannot hit water-borne enemies in any way.", "type": "Natural", "rarity": "Mythical", "introducedUpdate": "5", "m1Capability": true, "awakening": false, "combatRating": {"pvp": "Excellent", "grinding": "Good", "raids": "Excellent"}, "priceData": {"current": {"money": 2500000, "status": "AVAILABLE", "robux": 2300}, "sources": ["Blox Fruit Dealer", "Shop"]}, "transformation": true, "masteryRequirements": {"Normal Attack": 0, "Singularity": 1, "Orbital Chain": 100, "Gravitational Prison": 200, "Asteroid Crash": 300, "Shooting Star": 50}, "forms": [{"name": "Normal", "type": "Normal", "moves": [{"key": "TAP", "name": "Normal Attack", "description": "This move possesses three variations: * The user summons a small gravitational vortex at their cursor with a limited range, which explodes shortly after, dealing minor damage. Upgraded version has higher damage and a wider radius. * The user makes a swiping motion with their hand, creating a large gravitational sweep to the left, right, up and down. Upgraded version has higher damage and very high knockback. * The user summons a meteor from beyond the skies, landing at the location of their cursor, dealing high damage. Upgraded version summons more meteors, dealing higher damage.", "mastery": 0, "gif": "GravM1T", "gif1": "GravM1S", "gif2": "GravM1M", "damage": "2", "cooldown": "10", "energy": null, "furyMeter": null}, {"key": "Z", "name": "Singularity", "description": "The user creates a singularity within their hand, distorting spacetime, pulling in and stunning enemies whilst simultaneously distorting both parties' screens. The singularity then explodes in an AoE blast, knocking the enemies back.", "mastery": 1, "gif": "RGravZ", "damage": "9", "cooldown": "50", "energy": null, "furyMeter": null}, {"key": "X", "name": "Orbital Chain", "description": "The user creates a black hole right above them, which picks up and spews out countless debris in the direction of their cursor, dealing minor AoE damage to enemies. This move can be held down forever, however, after a few seconds the speed debris that strikes begins to slow down, causing both the damage and stun to weaken.", "mastery": 100, "gif": "RGravX", "damage": "13", "cooldown": "75", "energy": null, "furyMeter": null}, {"key": "C", "name": "Gravitational Prison", "description": "This move possesses two distinct variations: * If tapped: ** The user creates a large black hole in their hands, quickly releasing it in the direction of their cursor whilst pulling in enemies caught in its path. It then explodes, dealing substantial AoE damage. The black hole can be controlled using the cursor. * If held: ** The user uses 75% of their Gravitational Force meter to send a black hole into the sky, which starts absorbing matter and debris from the ground, pulling anyone unlucky enough to get caught in the pull. Once it has absorbed enough of the matter, the boulder will then expand under the pressure caused by the black hole and then explode, causing a very big explosion, after which chunks of debris will fall on the ground in a huge radius, dealing massive AoE damage alongside damage from the debris chunks. This move ignores all types of enemy armor except Shark V4's shield.", "mastery": 200, "gif": "GravityC2", "gif1": "UpgradedGravityC", "damage": "16", "cooldown": "90", "energy": null, "furyMeter": null}, {"key": "V", "name": "Asteroid Crash", "description": "This move possesses two distinct variations: * If tapped: ** The user creates a black hole in their hands before shooting it up towards the sky in a beam of gravitational force, summoning a large meteor that lands at the user's cursor, leaving a massive crater behind. * If held: ** The user uses all of their Gravitational Force to try trapping enemies in a specified area. If the attack hits, a cutscene plays to both the user and the enemies, bringing down airborne enemies and stunning both parties with the sky darkening in the process, showing the user ripping out pieces of the moon and sending them down on The Planet of Blox Fruits. The meteors then fall on the specified area, causing huge explosion and damage upon impact with the ground. This move ignores all types of enemy armor except Shark V4's shield.", "mastery": 300, "gif": "GravVT", "gif1": "GravVH", "damage": "23", "cooldown": "100", "energy": null, "furyMeter": null}, {"key": "F", "name": "Shooting Star", "description": "This move possesses two variations: * If tapped: ** The user summons 3 meteorites that is then shot in the direction of their cursor. * If held: ** On ground, the user summons materials from within their vicinity, forming a spherical orb of different materials upon which they can ride on. In air, the user creates an additional two meteorites that the user then shoots out. When released, this orb is launched in the direction of the user's cursor, dealing minor AoE damage. Additionally, all other Gravity moves can be used while this move is being used.", "mastery": 50, "gif": "GravFT", "gif1": "GravFHG", "gif2": "GravFHA", "damage": "5", "cooldown": "25", "energy": null, "furyMeter": null}], "images": {}}], "trivia": ["Gravity is the cheapest Mythical fruit in the game.", "The second rework concept of the Gravity moves was created by concept artist @crazy_rtx.", "The boss <PERSON><PERSON><PERSON> uses a slightly altered version of Gra<PERSON>, though the style of the moves resemble the old one.", "Gravity is one of the first two fruits that could obtain upgrades through the Admin Panel, the other being Eagle and soon, Creation.", "Gravity is one of six fruits to have a meter that is not intended for a transformation.", "Gravity is the only fruit in the game that requires the most tasks and items to fully upgrade it through the Admin Panel.", "Gravity is the second fruit to have a cutscene, the first being Control.", "Gravity currently has the longest cutscene of any fruit, being roughly 13.79 seconds.", "Gravity has been reworked two times and has two moves that are uncancellable.", "When going through doorways while flying, the boulder will purposely lower into the ground to allow the user to enter.", "C's upgraded version is very similar to an admin ability called \"Tensei\".", "The users are unable to use the max upgraded TAP while flying with Gravity. However, it still works even if they stop without ending the flight mode.", "A physical Gravity fruit can be dropped from defeating the Ty<PERSON> of the Skies, at a very low chance.", "Gravity and Blade are the only fruits that have had their entire moveset reworked twice."], "recommendations": ["It is recommended to defeat Bandits at Pirate Starter or Trainees at Marine Starter in the First Sea due to their low health.", "For the 1st and 2nd upgrade quest, it is recommended to kill the NPCs in the first sea, as they are highly underleveled and easy to kill.", "For the 3rd upgrade via the Admin Panel, it is recommended to ask a player, friend or person to spawn their boat.", "It is recommended to use F while staying out of water."], "changeHistoryAdvanced": [{"update": "26", "changes": [{"description": "Gravity was remade available to purchase.", "type": "general"}, {"description": "Gravity Fruit's model was slightly remade again.", "type": "general"}, {"description": "The color of the fruit model became darker.", "type": "general"}, {"description": "The pink ring now rotates around the fruit.", "type": "general"}, {"description": "Gravity Fruit's icon was remade.", "type": "general"}, {"description": "Added [M1] attacks.", "type": "addition"}, {"description": "All moves were reworked.", "type": "general"}, {"description": "[Z] Gravity Push was renamed to [Z] Singularity.", "type": "general"}, {"description": "[X] Gravity Obeisance was renamed to [X] Orbital Chain.", "type": "general"}, {"description": "[C] Meteor Pitch was renamed to [C] Gravitational Prison.", "type": "general"}, {"description": "[V] Meteors Rain was renamed to [V] Asteroid Crash.", "type": "general"}, {"description": "[F] Boulder Flight was renamed to [F] Shooting Star.", "type": "general"}], "type": "major_rework"}, {"update": "25", "changes": [{"description": "Gravity was made unavailable to purchase from the Blox Fruit Dealer's stock and Shop.", "type": "general"}], "type": "general"}, {"update": "20", "changes": [{"description": "Gravity Fruit's model was remade.", "type": "general"}], "type": "general"}, {"update": "17.3", "changes": [{"description": "Gravity was reworked.", "type": "general"}, {"description": "Every ability got a VFX rework.", "type": "general"}, {"description": "Old: [V] Meteors Rain could not be aimed and used to hit randomly.", "type": "general"}, {"description": "New: [V] Meteors Rain can be aimed.", "type": "addition"}], "type": "major_rework"}, {"update": "7", "changes": [{"description": "Made Gravity slightly more common.", "type": "general"}], "type": "general"}, {"update": "5", "changes": [{"description": "Gravity was released.", "type": "general"}], "type": "release"}], "economicData": {"acquisitionMethods": [{"source": "Blox Fruit Dealer", "type": "Shop"}], "reworkHistory": {}, "competitiveRanking": {"difficulty": "Highest in game"}, "marketAnalysis": {}}, "gallery": [{"url": "GravityWorldConceptArt.png", "caption": "Concept art of the [V] move \"The Planet\" posted on Twitter by @disardo1."}, {"url": "GravityBanner.png", "caption": "Shop artwork for the Gravity Fruit. Made by @EGOTISMS_RBLX."}], "shopQuote": "Allows the user to manipulate gravity, controlling the battlefield."}, "imageUrl": "https://static.wikia.nocookie.net/roblox-blox-piece/images/5/5f/Gravity_Fruit.png/revision/latest?cb=20250418030958", "imageUrls": ["https://static.wikia.nocookie.net/roblox-blox-piece/images/5/5f/Gravity_Fruit.png/revision/latest?cb=20250418030958", "https://static.wikia.nocookie.net/roblox-blox-piece/images/b/b5/GravitationalStepsofForce.gif/revision/latest?cb=20250418174108", "https://static.wikia.nocookie.net/roblox-blox-piece/images/a/ae/GravitationalForce.png/revision/latest?cb=20250419082355", "https://static.wikia.nocookie.net/roblox-blox-piece/images/e/ec/GravityDefense.gif/revision/latest?cb=20250425060333", "https://static.wikia.nocookie.net/roblox-blox-piece/images/9/92/GravityWorldConceptArt.png/revision/latest?cb=20250426041021", "https://static.wikia.nocookie.net/roblox-blox-piece/images/7/7c/GravityBanner.png/revision/latest?cb=20250516093105", "https://static.wikia.nocookie.net/roblox-blox-piece/images/6/6d/GravityIconOld.png/revision/latest?cb=20250523083109"], "lastUpdated": {"$date": "2025-07-14T18:44:24.316Z"}, "rawData": {"infobox": {"name": "Gravity", "image": "Gravity Fruit.png|Fruit Icon GravityFruitReworkedGif.gif|Fruit GIF Gravity.png|Icon", "type": "Natural", "rarity": "Mythical", "m1": "Yes", "update": "5", "money": "2,500,000"}, "wikitextLength": 23756, "movesFound": 12, "statsFound": 12, "extractedAt": "2025-07-14T18:44:24.317Z"}, "type": "fruit", "wikiUrl": "https://blox-fruits.fandom.com/wiki/Gravity"}