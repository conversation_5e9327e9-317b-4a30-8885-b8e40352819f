import fs from 'fs';

function analyzeDataCompleteness() {
  console.log('📊 Analyzing data completeness...\n');

  try {
    // Load scraped data
    const dragonScraped = JSON.parse(fs.readFileSync('lib/scrapers/Scraped.txt', 'utf-8'));
    const gravityScraped = JSON.parse(fs.readFileSync('lib/scrapers/Scraped2.txt', 'utf-8'));

    // Load raw data
    const dragonRaw = JSON.parse(fs.readFileSync('lib/scrapers/WIKITEXT.txt', 'utf-8'));
    const gravityRaw = JSON.parse(fs.readFileSync('lib/scrapers/WIKITEXT2.txt', 'utf-8'));

    const dragonWikitext = dragonRaw.query.pages[Object.keys(dragonRaw.query.pages)[0]].revisions[0]['*'];
    const gravityWikitext = gravityRaw.query.pages[Object.keys(gravityRaw.query.pages)[0]].revisions[0]['*'];

    console.log('🐉 DRAGON ANALYSIS');
    console.log('=' .repeat(50));
    analyzeFruit('Dragon', dragonScraped.fruitData, dragonWikitext);

    console.log('\n🌌 GRAVITY ANALYSIS');
    console.log('=' .repeat(50));
    analyzeFruit('Gravity', gravityScraped.fruitData, gravityWikitext);

  } catch (error) {
    console.error('❌ Error:', error instanceof Error ? error.message : String(error));
  }
}

function analyzeFruit(name: string, fruitData: any, wikitext: string) {
  let extractedCount = 0;
  let totalCount = 0;

  const sections = [
    { name: 'Basic Info', check: () => !!(fruitData.type && fruitData.rarity) },
    { name: 'Price Data', check: () => !!(fruitData.priceData?.current?.money && fruitData.priceData?.current?.robux) },
    { name: 'Stats Data', check: () => Object.keys(fruitData.statsData || {}).length > 0 },
    { name: 'Skill Data', check: () => Object.keys(fruitData.skillData || {}).length > 0 },
    { name: 'Passive Abilities', check: () => (fruitData.passiveAbilities?.length || 0) > 0 },
    { name: 'Overview (Pros/Cons)', check: () => !!(fruitData.pros?.length && fruitData.cons?.length) },
    { name: 'Gallery', check: () => (fruitData.gallery?.length || 0) > 0 },
    { name: 'Trivia', check: () => (fruitData.trivia?.length || 0) > 0 },
    { name: 'Change History', check: () => (fruitData.changeHistoryAdvanced?.length || 0) > 0 },
    { name: 'Upgrade Data', check: () => Object.keys(fruitData.upgradeData || {}).length > 0 },
    { name: 'Instinct Data', check: () => Object.keys(fruitData.instinctData || {}).length > 0 },
    { name: 'Skin System', check: () => !!fruitData.skinSystem },
    { name: 'Forms/Variants', check: () => (fruitData.forms?.length || 0) > 0 },
    { name: 'Economic Data', check: () => !!fruitData.economicData },
    { name: 'Mastery Requirements', check: () => !!fruitData.masteryRequirements }
  ];

  console.log('✅ EXTRACTED SECTIONS:');
  sections.forEach(section => {
    totalCount++;
    if (section.check()) {
      extractedCount++;
      console.log(`  ✓ ${section.name}`);
    }
  });

  console.log('\n❌ MISSING SECTIONS:');
  sections.forEach(section => {
    if (!section.check()) {
      console.log(`  ✗ ${section.name}`);
    }
  });

  // Detailed analysis
  console.log('\n📋 DETAILED ANALYSIS:');
  console.log(`  Stats Forms: ${Object.keys(fruitData.statsData || {}).length}`);
  console.log(`  Skill Forms: ${Object.keys(fruitData.skillData || {}).length}`);
  console.log(`  Passive Abilities: ${fruitData.passiveAbilities?.length || 0}`);
  console.log(`  Pros: ${fruitData.pros?.length || 0}`);
  console.log(`  Cons: ${fruitData.cons?.length || 0}`);
  console.log(`  Gallery Items: ${fruitData.gallery?.length || 0}`);
  console.log(`  Trivia Items: ${fruitData.trivia?.length || 0}`);
  console.log(`  Change History: ${fruitData.changeHistoryAdvanced?.length || 0}`);
  console.log(`  Upgrade Entries: ${Object.keys(fruitData.upgradeData || {}).length}`);
  console.log(`  Instinct Entries: ${Object.keys(fruitData.instinctData || {}).length}`);
  console.log(`  Forms/Variants: ${fruitData.forms?.length || 0}`);

  const completeness = (extractedCount / totalCount) * 100;
  console.log(`\n🎯 COMPLETENESS: ${completeness.toFixed(1)}% (${extractedCount}/${totalCount})`);

  // Check for missing critical data
  console.log('\n🔍 CRITICAL DATA CHECK:');
  
  // Check if Tips & Tricks section exists in raw but not extracted
  if (wikitext.includes('Tips & Tricks') || wikitext.includes('==Tips==')) {
    console.log('  ⚠️  Tips & Tricks section exists in raw data but not extracted');
  }
  
  // Check for specific missing patterns
  if (name === 'Dragon') {
    if (wikitext.includes('Dragon Token') && !fruitData.trivia?.some((t: string) => t.includes('Dragon Token'))) {
      console.log('  ⚠️  Dragon Token information partially missing from trivia');
    }
    if (wikitext.includes('Skins') && !fruitData.skinSystem) {
      console.log('  ⚠️  Skin system information not fully extracted');
    }
  }
  
  if (name === 'Gravity') {
    if (wikitext.includes('Upgrading') && Object.keys(fruitData.upgradeData || {}).length === 0) {
      console.log('  ⚠️  Upgrade data missing despite being in raw data');
    }
    if (wikitext.includes('Instinct Chart') && Object.keys(fruitData.instinctData || {}).length === 0) {
      console.log('  ⚠️  Instinct data missing despite being in raw data');
    }
  }
}

analyzeDataCompleteness();
