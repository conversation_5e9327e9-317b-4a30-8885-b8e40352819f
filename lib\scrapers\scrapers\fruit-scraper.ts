import { BaseScraper } from "../base-scraper"
import { ScrapedItem, FruitData, PriceData, FruitForm } from "../types"
import { EnhancedStatsExtractor } from "../enhanced-stats-extractor"
import fetch from "node-fetch"

export class FruitScraper extends BaseScraper {
  private rawData = {
    movesFound: 0,
    statsFound: 0
  };
  private extractUpgradingData(wikitext: string): FruitData['upgrades'] {
  const upgrades: NonNullable<FruitData['upgrades']> = [];
  const upgradingSection = this.extractFromWikiSection(wikitext, 'Upgrading');
  if (!upgradingSection) return undefined;

  const rows = upgradingSection.split('|-').slice(1); // Split by rows, skip header

  for (const row of rows) {
    const cells = this.splitTableRowSafe(row);
    if (cells.length < 5) continue;

    const name = this.cleanWikitext(cells[0]);
    const mastery = this.parseNumber(this.cleanWikitext(cells[1]));
    const research = this.cleanWikitext(cells[2]);
    const changes = this.cleanWikitext(cells[3]);
    const costText = cells[4];

    if (!name || !mastery) continue;

    const cost: { fragments?: number; materials?: Record<string, number> } = {
      materials: {}
    };

    // Parse cost
    const fragmentMatch = costText.match(/\{\{Fragment\|([\d,]+)\}\}/);
    if (fragmentMatch) {
      cost.fragments = this.parseNumber(fragmentMatch[1]);
    }

    const materialMatches = costText.match(/\{\{([^|]+)\|(\d+)\}\}/g);
    if (materialMatches) {
      materialMatches.forEach(match => {
        const parts = match.match(/\{\{([^|]+)\|(\d+)\}\}/);
        if (parts && parts[1].toLowerCase() !== 'fragment') {
          const materialName = this.cleanWikitext(parts[1]);
          const quantity = parseInt(parts[2]);
          if (materialName && !isNaN(quantity) && cost.materials) {
            cost.materials[materialName] = quantity;
          }
        }
      });
    }

    upgrades.push({
      name,
      mastery,
      research,
      changes,
      cost
    });
   }

  return upgrades.length > 0 ? upgrades : undefined;
} 

  
  extractSpecificData(wikitext: string, infoboxData: Record<string, string>): FruitData {
    const fruitData: FruitData = {};
    const upgrades = this.extractUpgradingData(wikitext);
    if (upgrades) {
     fruitData.upgrades = upgrades;
    }

    // Utilisation de l'enhanced stats extractor avec logique spécifique aux fruits
    const enhancedExtractor = new EnhancedStatsExtractor();
    
    // Extraction avec logique spécifique au type de fruit (Dragon, Gravity, Standard)
    const extractedData = enhancedExtractor.extractWithFruitSpecificLogic(wikitext);
    
    // Appliquer les données extraites
    if (Object.keys(extractedData.statsData).length > 0) {
        fruitData.statsData = extractedData.statsData;
    }

    if (Object.keys(extractedData.overviewData).length > 0) {
        fruitData.overviewData = extractedData.overviewData;
    }

    if (Object.keys(extractedData.skillData).length > 0) {
        fruitData.skillData = extractedData.skillData;
    }

    if (extractedData.passiveAbilities.length > 0) {
        fruitData.passiveAbilities = extractedData.passiveAbilities;
    }

    // Extraction des Instinct Charts (pour Gravity et autres fruits)
    const instinctData = enhancedExtractor.extractInstinctChartData(wikitext);
    if (Object.keys(instinctData).length > 0) {
        fruitData.instinctData = instinctData;
    }

    // Extraction des upgrades Admin Panel (pour Gravity)
    const upgradeData = enhancedExtractor.extractUpgradeData(wikitext);
    if (Object.keys(upgradeData).length > 0) {
        fruitData.upgradeData = upgradeData;
    }

    // Extraction des variants (pour Dragon East/West)
    const variantData = enhancedExtractor.extractVariantData(wikitext);
    if (Object.keys(variantData).length > 0) {
        fruitData.variantData = variantData;
    }

    // Extraction des résistances aux dégâts (pour Dragon)
    const damageResistance = enhancedExtractor.extractDamageResistance(wikitext);
    if (Object.keys(damageResistance).length > 0) {
        fruitData.damageResistance = damageResistance;
    }

    // Amélioration de l'extraction de la description principale
    const mainDescription = this.extractMainDescription(wikitext, infoboxData.name || "");
    if(mainDescription) {
        fruitData.mainDescription = mainDescription;
    } else {
        // Fallback sur la description de l'infobox
        if (infoboxData.description) {
            fruitData.mainDescription = this.cleanWikitext(infoboxData.description);
        }
    }

    // Amélioration de l'extraction du type
    if (infoboxData.type) {
      type ValidFruitType = "Natural" | "Elemental" | "Beast" | "Ancient Zoan" | "Mythical Zoan" | "Logia" | "Paramecia" | "Zoan";
      const validTypes: ValidFruitType[] = ["Natural", "Elemental", "Beast", "Ancient Zoan", "Mythical Zoan", "Logia", "Paramecia", "Zoan"];
      let type = infoboxData.type.trim();
      
      // Normalisation du type
      if (type.toLowerCase().includes("zoan")) {
        if (type.toLowerCase().includes("ancient")) {
          type = "Ancient Zoan";
        } else if (type.toLowerCase().includes("mythical")) {
          type = "Mythical Zoan";
        } else {
          type = "Zoan";
        }
      } else {
        type = type.charAt(0).toUpperCase() + type.slice(1).toLowerCase();
      }
      
      if (validTypes.includes(type as ValidFruitType)) {
        fruitData.type = type as ValidFruitType;
      }
    }

    // Amélioration de l'extraction de la rareté
    if (infoboxData.rarity) {
      const validRarities = ["Common", "Uncommon", "Rare", "Legendary", "Mythical"] as const;
      const rarity = infoboxData.rarity.trim().charAt(0).toUpperCase() + infoboxData.rarity.trim().slice(1).toLowerCase();
      if (validRarities.includes(rarity as any)) {
        fruitData.rarity = rarity as typeof validRarities[number];
      }
    }

    // Extract introduced update with multiple key variants and cleaning
    const updateValue = infoboxData.update || infoboxData.introduced || infoboxData.introduction;
    if (updateValue) {
      const cleanUpdate = this.cleanWikitext(updateValue.trim())
        .replace(/^Update /i, '')  // Remove "Update " prefix if present
        .replace(/^v/i, '')        // Remove "v" prefix if present
        .trim();
      if (cleanUpdate) {
        fruitData.introducedUpdate = cleanUpdate;
      }
    }

    // Extract M1 capability with multiple key and value variants
    const m1Value = infoboxData.m1 || infoboxData.m1_capability || infoboxData.m1capability;
    if (m1Value) {
      const m1Clean = m1Value.toLowerCase().trim();
      fruitData.m1Capability = ['yes', 'true', 'available', '1', 'y'].includes(m1Clean);
    }

    // Extract awakening information - check both infobox and content
    let hasAwakening = false;
    
    // Check infobox field first
    if (infoboxData.awakening) {
      const awakeningValue = infoboxData.awakening.toLowerCase().trim();
      hasAwakening = ['yes', 'true', 'available'].includes(awakeningValue);
    }
    
    // Check for awakening section in content (primary method)
    const awakeningSection = wikitext.match(/==\s*Awakening\s*==([\s\S]*?)(?===|$)/i);
    if (awakeningSection) {
      hasAwakening = true;
      const awakeningContent = awakeningSection[1];
      
      // Extract requirements from lists and paragraphs
      const requirementLines = [
        ...awakeningContent.split('\n')
          .filter(line => line.trim().startsWith('*'))
          .map(line => this.cleanWikitext(line.replace('*', '').trim())),
        ...awakeningContent.split('\n')
          .filter(line => !line.trim().startsWith('*') && line.trim().length > 20)
          .map(line => this.cleanWikitext(line.trim()))
      ].filter(line => line.length > 0);
      
      if (requirementLines.length > 0) {
        fruitData.awakeningRequirements = requirementLines;
      }
    }
    
    fruitData.awakening = hasAwakening;

    // Extract transformation information and requirements
    const transformationSection = wikitext.match(/==\s*Transformation\s*==([\s\S]*?)(?===|$)/i);
    if (transformationSection) {
      fruitData.transformation = true;
      const transformationContent = transformationSection[1];
      
      // Extract requirements from lists and paragraphs
      const requirementLines = [
        ...transformationContent.split('\n')
          .filter(line => line.trim().startsWith('*'))
          .map(line => this.cleanWikitext(line.replace('*', '').trim())),
        ...transformationContent.split('\n')
          .filter(line => !line.trim().startsWith('*') && line.trim().length > 20)
          .map(line => this.cleanWikitext(line.trim()))
      ].filter(line => line.length > 0);
      
      if (requirementLines.length > 0) {
        fruitData.transformationRequirements = requirementLines;
      }
    }

    // Extract combat ratings with improved detection
    const combatRating: FruitData['combatRating'] = {};
    
    // Patterns plus détaillés pour les ratings (priorité de haut en bas)
    const ratingPatterns = {
      pvp: [
        /most\s+powerful[\s\S]*?pvp/i,
        /excellent(?:\s+choice)?\s+(?:for|in)\s+pvp/i,
        /very\s+good\s+(?:for|in)\s+pvp/i,
        /good\s+(?:for|in)\s+pvp/i,
        /average\s+(?:for|in)\s+pvp/i,
        /poor\s+(?:for|in)\s+pvp/i
      ],
      grinding: [
        /excellent\s+(?:for|in)\s+grinding/i,
        /very\s+good\s+(?:for|in)\s+grinding/i,
        /good\s+(?:for|in)\s+grinding/i,
        /average\s+(?:for|in)\s+grinding/i,
        /poor\s+(?:for|in)\s+grinding/i
      ],
      raids: [
        /excellent[\s\S]*?raids?/i,
        /excellent\s+(?:for|in)\s+raids?/i,
        /very\s+good\s+(?:for|in)\s+raids?/i,
        /good\s+(?:for|in)\s+raids?/i,
        /average\s+(?:for|in)\s+raids?/i,
        /poor\s+(?:for|in)\s+raids?/i
      ]
    };

    // Helper function pour trouver le rating
    const findRating = (patterns: RegExp[]): "Excellent" | "Good" | "Average" | "Poor" | undefined => {
      for (let i = 0; i < patterns.length; i++) {
        if (patterns[i].test(wikitext)) {
          if (i <= 1) return "Excellent"; // Indices 0 et 1 sont "Excellent"
          if (i <= 3) return "Good";      // Indices 2 et 3 sont "Good"
          if (i === 4) return "Average";
          return "Poor"; // Le reste est "Poor"
        }
      }
      return undefined;
    };

    // Extraire les ratings pour chaque catégorie
    const pvpRating = findRating(ratingPatterns.pvp);
    if (pvpRating) combatRating.pvp = pvpRating;

    const grindingRating = findRating(ratingPatterns.grinding);
    if (grindingRating) combatRating.grinding = grindingRating;

    const raidsRating = findRating(ratingPatterns.raids);
    if (raidsRating) combatRating.raids = raidsRating;

    if (Object.keys(combatRating).length > 0) {
      fruitData.combatRating = combatRating;
    }

    // Extract detailed move stats with improved parsing
    const moveStatsSection = wikitext.match(/==\s*Move Stats\s*==([\s\S]*?)(?===|$)/i);
    if (moveStatsSection) {
      const moveStats: NonNullable<FruitData['detailedMoveStats']> = {};
      const statsContent = moveStatsSection[1];
      
      // Try different table formats
      const tableRegex = /\|\s*([^|]+)\s*\|\s*([^|]+)\s*\|\s*([^|]+)\s*\|\s*([^|]+)(?:\s*\|\s*([^|]+))?\s*(?:\||$)/gm;
      let tableMatch;
      const tableMatches = [];
      while ((tableMatch = tableRegex.exec(statsContent)) !== null) {
        tableMatches.push(tableMatch);
      }

      for (const match of tableMatches) {
        const [_, moveName, damage, cooldown, energy, description] = match;
        if (!moveName?.trim()) continue;

        const cleanMoveName = this.cleanWikitext(moveName.trim());
        const formKey = "General";

        if (!moveStats[formKey]) {
          moveStats[formKey] = {} as Record<string, any[]>;
        }

        if (!moveStats[formKey][cleanMoveName]) {
          moveStats[formKey][cleanMoveName] = [];
        }

        moveStats[formKey][cleanMoveName].push({
          damage: damage ? this.cleanWikitext(damage.trim()) : undefined,
          cooldown: cooldown ? this.cleanWikitext(cooldown.trim()) : undefined,
          energy: energy ? this.cleanWikitext(energy.trim()) : undefined,
          description: description ? this.cleanWikitext(description.trim()) : undefined
        });
      }

      if (Object.keys(moveStats).length > 0) {
        fruitData.detailedMoveStats = moveStats;
      }
    }

    // Extract status effects with improved validation
    const statusEffects: NonNullable<FruitData['statusEffects']> = [];
    const effectsSection = wikitext.match(/==\s*Status Effects\s*==([\s\S]*?)(?===|$)/i);
    
    if (effectsSection) {
      const effectsContent = effectsSection[1];
      const effectRegex = /\|\s*([^|]+)\s*\|\s*([^|]+)\s*(?:\|\s*([^|]+)\s*)?(?:\|\s*([^|]+)\s*)?/g;
      let effectMatch;
      const effectMatches = [];
      while ((effectMatch = effectRegex.exec(effectsContent)) !== null) {
        effectMatches.push(effectMatch);
      }

      for (const match of effectMatches) {
        const [_, name, description, duration, damage] = match;
        if (!name?.trim() || !description?.trim()) continue;

        const effect = {
          name: this.cleanWikitext(name.trim()),
          description: this.cleanWikitext(description.trim()),
          duration: duration ? this.cleanWikitext(duration.trim()) : undefined,
          damage: damage ? this.cleanWikitext(damage.trim()) : undefined
        };

        // Valider que nous avons au moins des informations utiles
        if (effect.name.length > 0 && effect.description.length > 10) {
          statusEffects.push(effect);
        }
      }

      if (statusEffects.length > 0) {
        fruitData.statusEffects = statusEffects;
      }
    }

    // NOUVEAU: Extraction complète des données de prix
    const priceData = this.extractPriceData(wikitext, infoboxData);
    if (Object.keys(priceData.current).length > 0 || priceData.historical || priceData.discounts || priceData.sources) {
      fruitData.priceData = priceData;
    }

    // Extract spawn and stock chances
    if (infoboxData.stock_chance || infoboxData.stockchance) {
      const stockChance = this.parseNumber(infoboxData.stock_chance || infoboxData.stockchance)
      if (stockChance) fruitData.stockChance = stockChance
    }

    if (infoboxData.spawn_chance || infoboxData.spawnchance) {
      const spawnChance = this.parseNumber(infoboxData.spawn_chance || infoboxData.spawnchance)
      if (spawnChance) fruitData.spawnChance = spawnChance
    }

    // Detect transformation
    if (wikitext.toLowerCase().includes("transformation") || wikitext.toLowerCase().includes("transform")) {
      fruitData.transformation = true
    }

  // AMÉLIORÉ: Extract passive abilities avec plus de détails
    const passiveAbilities = this.extractPassiveAbilities(wikitext)
    // NOUVEAU: Extraction robuste des passifs secondaires (Draconic Blood, Monarch’s Scales, etc.)
    const secondaryPassives = [];
    const secondaryPattern = /'''(Draconic Blood|Draconic Monarch's Scales|Strong Hair\/Saddle|Skins)'''[\s\S]*?(?=\n\|-\n|'''[^']+'''|\n\|<center>|$)/gi;
    let match;
    while ((match = secondaryPattern.exec(wikitext)) !== null) {
      const name = match[1].trim();
      let desc = match[0].replace(/'''[^']+'''/, '').trim();
      desc = this.cleanWikitext(desc).replace(/^\*+/, '').replace(/\n+/g, ' ').replace(/\s+/g, ' ').trim();
      if (name && desc && !passiveAbilities.some(p => p.name === name)) {
        secondaryPassives.push({ name, description: desc });
      }
    }
    const allPassives = [...passiveAbilities, ...secondaryPassives];
    if (allPassives.length > 0) {
      fruitData.passiveAbilities = allPassives;
    }

    // NOUVEAU: Extract Fury Meter mechanics
    const furyMeterMechanics = this.extractFuryMeterMechanics(wikitext);
    if (furyMeterMechanics) {
      fruitData.furyMeterMechanics = furyMeterMechanics;
    }

    // NOUVEAU: Extract damage resistance data by form
    const legacyDamageResistance = this.extractDamageResistanceData(wikitext);
    // Structure explicite : { transformed, hybrid: {players, npcs}, human }
    if (legacyDamageResistance) {
      fruitData.damageResistance = {
        transformed: legacyDamageResistance.transformed || "0%",
        hybrid: legacyDamageResistance.hybrid || { players: "0%", npcs: "0%" },
        human: "0%"
      };
    }

    // Extract mastery requirements from moves (improved to handle any order)
    const masteryRequirements: Record<string, number> = {}
    const skillBoxMatches = wikitext.match(/\{\{SkillBox\|[\s\S]*?\}\}/g)
    if (skillBoxMatches) {
      skillBoxMatches.forEach(skillBox => {
        const masteryMatch = skillBox.match(/\|Mas\s*=\s*(\d+)/)
        const moveMatch = skillBox.match(/\|Move\s*=\s*([^|\n}]+)/)
        if (masteryMatch && moveMatch) {
          const mastery = parseInt(masteryMatch[1])
          const moveName = this.cleanWikitext(moveMatch[1])
          if (!isNaN(mastery) && moveName) {
            masteryRequirements[moveName] = mastery
          }
        }
      })
    }

    if (Object.keys(masteryRequirements).length > 0) {
      fruitData.masteryRequirements = masteryRequirements
    }

    // AMÉLIORÉ: Extraction des formes et mouvements
    const forms = this.extractForms(wikitext, infoboxData);
    if (forms.length > 0) {
      // Nettoyer les noms des formes du wikitext brut
      forms.forEach(form => {
        if (form.name.includes("{{")) {
          form.name = this.cleanWikitext(form.name);
        }
        // Incrémenter le compteur de mouvements trouvés
        if (form.moves) {
          this.rawData.movesFound += form.moves.length;
        }
      });
      fruitData.forms = forms;
    }
    
    // AMÉLIORÉ: Attacher les Avantages/Inconvénients à chaque forme
    const legacyOverviewData = this.extractOverviewData(wikitext);
    if (fruitData.forms && Object.keys(legacyOverviewData).length > 0) {
      fruitData.forms.forEach((form: FruitForm) => {
        // Find matching overview data, allowing for partial matches like "Transformed (East)" matching "Transformed East"
        const overviewKey = Object.keys(legacyOverviewData).find(key => 
          form.name.replace(/[()]/g, "").trim().toLowerCase() === key.replace(/[()]/g, "").trim().toLowerCase()
        );
        if (overviewKey && legacyOverviewData[overviewKey]) {
          form.pros = legacyOverviewData[overviewKey].pros;
          form.cons = legacyOverviewData[overviewKey].cons;
        }
      });
      // Fallback : si aucune forme n’a reçu de pros/cons et qu’il n’y a qu’une seule forme et un seul set de pros/cons, les attacher à la forme unique
      const anyFormHasPros = fruitData.forms.some(f => f.pros && f.pros.length > 0);
      const anyFormHasCons = fruitData.forms.some(f => f.cons && f.cons.length > 0);
      if ((!anyFormHasPros && !anyFormHasCons) && fruitData.forms.length === 1 && Object.keys(legacyOverviewData).length === 1) {
        const onlyKey = Object.keys(legacyOverviewData)[0];
        fruitData.forms[0].pros = legacyOverviewData[onlyKey].pros;
        fruitData.forms[0].cons = legacyOverviewData[onlyKey].cons;
      }
    }

    // AMÉLIORÉ: Extraction trivia information
    const triviaMatch = wikitext.match(/==\s*Trivia\s*==([\s\S]*?)(?===|$)/i)
    if (triviaMatch) {
      const trivia = this.extractListItems(triviaMatch[1])
        .filter(line => line.length > 15 && !line.toLowerCase().includes('category:'))
        .slice(0, 15)
      if (trivia.length > 0) {
        fruitData.trivia = trivia
      }
    }

    // AMÉLIORÉ: Extract race/style recommendations
    const recommendations: string[] = []
    
    // Extract race recommendations
    const raceRecommendations = wikitext.match(/It is recommended to use \[\[([^\]]+)\]\]/gi)
    if (raceRecommendations) {
      const raceLinks = this.extractWikiLinks(raceRecommendations.join(' '))
      recommendations.push(...raceLinks)
    }

    // Extract general recommendations (exclude negative ones)
    const generalRecommendations = wikitext.match(/[^.]*?recommended[^.]*\./gi)
    if (generalRecommendations) {
      generalRecommendations.forEach(rec => {
        const cleanRec = this.cleanWikitext(rec)
        // Exclure les recommendations négatives
        if (cleanRec.length > 20 && cleanRec.length < 200 && 
            !cleanRec.toLowerCase().includes('not recommended') &&
            !cleanRec.toLowerCase().includes('isn\'t recommended') &&
            !cleanRec.toLowerCase().includes('is not recommended') &&
            !cleanRec.toLowerCase().includes('are not recommended') &&
            !cleanRec.toLowerCase().includes('aren\'t recommended')) {
          
          // Nettoyer et formater la recommendation
          let formattedRec = cleanRec.trim();
          
          // Nettoyer les parenthèses orphelines et caractères indésirables
          formattedRec = formattedRec
            .replace(/^\(+/, '')  // Supprimer parenthèses ouvrantes en début
            .replace(/\)+$/, '')  // Supprimer parenthèses fermantes en fin
            .replace(/\([^)]*$/, '') // Supprimer parenthèse ouverte sans fermeture
            .replace(/^[^(]*\)/, '') // Supprimer fermeture sans ouverture
            .trim();
          
          // S'assurer que ça commence par une majuscule
          if (formattedRec.length > 0) {
            formattedRec = formattedRec.charAt(0).toUpperCase() + formattedRec.slice(1);
          }
          
          // Vérifier que la recommendation est encore valide après nettoyage
          if (formattedRec.length > 10) {
            recommendations.push(formattedRec);
          }
        }
      })
    }

    // NOUVEAU: Chercher spécifiquement des recommendations positives dans les sections Tips/Upgrading
    const tipsSection = this.extractFromWikiSection(wikitext, 'Tips');
    const upgradingSection = this.extractFromWikiSection(wikitext, 'Upgrading');
    
    [tipsSection, upgradingSection].forEach(section => {
      if (section) {
        const sectionRecs = section.match(/It is recommended to[^.]*\./gi);
        if (sectionRecs) {
          sectionRecs.forEach(rec => {
            const cleanRec = this.cleanWikitext(rec);
            if (cleanRec.length > 15 && cleanRec.length < 150) {
              recommendations.push(cleanRec);
            }
          });
        }
      }
    });

    if (recommendations.length > 0) {
      const uniqueRecommendations = Array.from(new Set(recommendations)).slice(0, 8);
      fruitData.recommendations = uniqueRecommendations;
    }

    // Extract counter information
    const counters: string[] = []
    const counterMatches = wikitext.match(/good counter to[^.]*\./gi)
    if (counterMatches) {
      counterMatches.forEach(counter => {
        const cleanCounter = this.cleanWikitext(counter)
        if (cleanCounter.length > 10) {
          counters.push(cleanCounter)
        }
      })
    }

    // Extract fruits mentioned as counters
    const fruitCounters = wikitext.match(/counter to fruits like \[\[([^\]]+)\]\]/gi)
    if (fruitCounters) {
      fruitCounters.forEach(match => {
        const fruits = match.match(/\[\[([^\]]+)\]\]/g)
        if (fruits) {
          fruits.forEach(fruit => {
            const fruitName = fruit.replace(/\[\[|\]\]/g, '')
            counters.push(`Counters ${fruitName}`)
          })
        }
      })
    }

    if (counters.length > 0) {
      const uniqueCounters = Array.from(new Set(counters)).slice(0, 5);
      fruitData.counters = uniqueCounters;
    }

    // NOUVEAU: Extraction complète des variantes (East/West)
    const variantsComplete = this.extractVariantDataComplete(wikitext);
    if (variantsComplete) {
      fruitData.variantsComplete = variantsComplete;
    }
    
    // Fallback: ancienne méthode pour rétrocompatibilité
    const variants = this.extractVariants(wikitext)
    if ((variants ?? []).length > 0) {
      fruitData.variants = variants
    }

    // NOUVEAU: Extraction des détails de la refonte (ex: Dragon Token)
    const reworkDetails = this.extractReworkDetails(wikitext);
    if (reworkDetails) {
      fruitData.reworkDetails = reworkDetails;
    }

    // NOUVEAU: Extraction complète du système de skins
    const skinSystemComplete = this.extractSkinSystemComplete(wikitext);
    if (skinSystemComplete) {
      fruitData.skinSystem = skinSystemComplete;
    }
    
    // Fallback: ancienne méthode pour rétrocompatibilité
    const skins = this.extractSkins(wikitext)
    if (skins && skins.length > 0) {
      fruitData.skins = skins
    }

    // NOUVEAU: Extract enhanced change history with categorization
    const changeHistoryAdvanced = this.extractChangeHistoryAdvanced(wikitext);
    if (changeHistoryAdvanced) {
      fruitData.changeHistoryAdvanced = changeHistoryAdvanced;
    }

    // Fallback: Extract basic change history for compatibility
    const changeHistoryMatch = wikitext.match(/==\s*Change History\s*==([\s\S]*?)(?===|$)/i)
    if (changeHistoryMatch) {
      const changeHistory: Array<{ update: string; changes: string[] }> = []
      const historyContent = changeHistoryMatch[1]

      // Extract updates
      const updateMatches = historyContent.match(/\{\{Update\|([^\}]+)\}\}([\s\S]*?)(?=\{\{Update\||----|\n\}\}|$)/g)
      if (updateMatches) {
        updateMatches.forEach(updateBlock => {
          const updateMatch = updateBlock.match(/\{\{Update\|([^\]]+)\}\} /)
          if (updateMatch) {
            const updateNumber = updateMatch[1].trim()
            const changes: string[] = []

            // Extract changes
            const changeLines = updateBlock.split('\n').filter(line => line.trim().startsWith('*'))
            changeLines.forEach(line => {
              const cleanChange = this.cleanWikitext(line.replace('*', '').trim())
              if (cleanChange.length > 5) {
                changes.push(cleanChange)
              }
            })

            if (changes.length > 0) {
              changeHistory.push({
                update: updateNumber,
                changes: changes
              })
            }
          }
        })
      }

      if (changeHistory.length > 0) {
        fruitData.changeHistory = changeHistory
      }
    }

    // NOUVEAU: Extract enhanced economic data
    const economicDataAdvanced = this.extractEconomicDataAdvanced(wikitext);
    if (economicDataAdvanced) {
      fruitData.economicData = economicDataAdvanced;
    }

    // Look for additional value information in text
    const valueMatch = wikitext.match(/(?:worth|value|costs?)\s*(?:of\s*)?(?:approximately\s*)?[\$₿]?([\d,]+)/i)
    if (valueMatch && !fruitData.value) {
      const value = this.parseNumber(valueMatch[1])
      if (value) fruitData.value = value
    }

    // NOUVEAU: Extraction de la galerie
    const gallery = this.extractGallery(wikitext);
    if (gallery.length > 0) {
      fruitData.gallery = gallery;
    }

    // Ajouter la citation de boutique si présente
    const quote = this.extractShopQuote(wikitext);
    if (quote) fruitData.shopQuote = quote;

    return fruitData
  }

  // UTILITAIRES: Méthodes communes pour éviter les répétitions
  private extractFromWikiSection(wikitext: string, sectionName: string): string | null {
    const sectionRegex = new RegExp(`==\\s*${sectionName}\\s*==([\\s\\S]*?)(?===|$)`, 'i');
    const match = wikitext.match(sectionRegex);
    return match ? match[1].trim() : null;
  }

  // NOUVEAU: Trouve le tabber qui contient des SkillBox
  private findMovesTabber(wikitext: string): string | null {
    // Méthode 1: Chercher les {{#tag:tabber qui contiennent SkillBox
    const tagTabberMatches = wikitext.match(/{{#tag:tabber\|[\s\S]*?\}\}/g);
    if (tagTabberMatches) {
      for (const tabber of tagTabberMatches) {
        if (tabber.includes('{{SkillBox') && tabber.includes('{{SkillStart}}')) {
          return tabber;
        }
      }
    }
    
    // Méthode 2: Chercher les <tabber> qui contiennent SkillBox
    const classicTabberMatches = wikitext.match(/<tabber>[\s\S]*?<\/tabber>/g);
    if (classicTabberMatches) {
      for (const tabber of classicTabberMatches) {
        if (tabber.includes('{{SkillBox') && tabber.includes('{{SkillStart}}')) {
          return tabber;
        }
      }
    }
    
    return null;
  }

  /**
   * IMPROVED: Robust tabber parser that handles complex nested structures
   * Supports both {{#tag:tabber|...}} and <tabber>...</tabber> formats
   */
  private parseTabber(tabberWikitext: string): Record<string, string> {
    const tabs: Record<string, string> = {};
    if (!tabberWikitext) return tabs;

    let clean = '';
    let isNewFormat = false;
    
    // Extract content with robust brace counting for {{#tag:tabber|...}}
    if (tabberWikitext.includes("{{#tag:tabber|")) {
      const startPattern = "{{#tag:tabber|";
      const startIndex = tabberWikitext.indexOf(startPattern);
      if (startIndex !== -1) {
        let braceCount = 0;
        let endIndex = startIndex;
        let i = startIndex;
        
        while (i < tabberWikitext.length) {
          if (tabberWikitext.substring(i, i + 2) === '{{') {
            braceCount++;
            i += 2;
          } else if (tabberWikitext.substring(i, i + 2) === '}}') {
            braceCount--;
            i += 2;
            if (braceCount === 0) {
              endIndex = i - 2; // Position before the final }}
              break;
            }
          } else {
            i++;
          }
        }
        
        if (endIndex > startIndex) {
          clean = tabberWikitext.substring(startIndex + startPattern.length, endIndex).trim();
          isNewFormat = true;
        }
      }
    } else if (tabberWikitext.includes("<tabber>")) {
      // Format <tabber>...</tabber>
      const match = tabberWikitext.match(/<tabber>([\s\S]*?)<\/tabber>/);
      if (match) {
        clean = match[1].trim();
        isNewFormat = false;
      }
    }

    if (!clean) return tabs;

    let tabSections: string[] = [];
    
    if (isNewFormat) {
      // Format {{#tag:tabber avec séparateur {{!}}-{{!}}
      tabSections = clean.split(/\{\{!\}\}-\{\{!\}\}/g);
    } else {
      // Format <tabber> avec séparateur |-|
      if (clean.includes('|-|')) {
        tabSections = clean.split(/\|-\|/g);
      } else {
        // Fallback: try splitting on newlines with '=' 
        const lines = clean.split('\n');
        let currentSection = '';
        let currentTab = '';
        
        for (const line of lines) {
          if (line.includes('=') && !line.includes('{{') && !line.includes('|=')) {
            if (currentTab && currentSection) {
              tabs[currentTab] = currentSection.trim();
            }
            const parts = line.split('=');
            currentTab = parts[0].trim();
            currentSection = parts.slice(1).join('=');
          } else {
            currentSection += '\n' + line;
          }
        }
        
        if (currentTab && currentSection) {
          tabs[currentTab] = currentSection.trim();
        }
        
        return tabs;
      }
    }

    // Process tab sections
    for (const section of tabSections) {
      const eqIdx = section.indexOf('=');
      if (eqIdx === -1) continue;
      
      let tabName = section.slice(0, eqIdx).trim();
      const tabContent = section.slice(eqIdx + 1).trim();
      
      // Clean tab names
      tabName = tabName
        .replace(/^\n+/, '')
        .replace(/\n+$/, '')
        .replace(/<\/?tabber>/gi, '')
        .replace(/^\s*\|\s*/, '') // Remove leading pipes
        .trim();
      
      // Only add valid tabs
      if (tabName && tabContent && tabName.length > 0 && !tabName.includes("{{") && !tabName.includes("<")) {
        tabs[tabName] = tabContent;
      }
    }
    
    return tabs;
  }

  private extractListItems(content: string, marker: string = '*'): string[] {
    if (!content) return [];
    return content.split('\n')
      .filter(line => line.trim().startsWith(marker))
      .map(line => this.cleanWikitext(line.replace(marker, '').trim()))
      .filter(line => {
        const l = line.trim();
        if (l.length <= 10) return false;
        if (/type=/i.test(l)) return false;         // Ex: type=Normal
        if (/^[A-Za-z0-9_-]+=/.test(l)) return false; // Ex: General=
        if (/\[[A-Za-z]/.test(l)) return false;    // Ex: [TAP] Normal Attack
        if (/general/i.test(l)) return false;       // Mot-clé générique
        if (/^\[tap|\[z|\[x|\[c|\[v|\[f/i.test(l)) return false; // starts with move tag
        return true;
      });
  }

  private extractTableCells(row: string): string[] {
    return row.split('|')
      .map(cell => cell.trim())
      .filter(cell => cell && !cell.startsWith('{'))
  }

  private extractWikiLinks(text: string): string[] {
    if (!text) return []
    const links = text.match(/\[\[([^\]|]+)/g)
    return links ? links.map(link => link.replace('[[', '')) : []
  }

  private extractShopQuote(wikitext: string): string {
    const quoteMatch = wikitext.match(/\{\{Quote\|([^|]+)\|/i);
    return quoteMatch ? quoteMatch[1].trim() : "";
  }

  private extractMainDescription(wikitext: string, name: string): string {
    const escapedName = this.cleanWikitext(name).replace(/[.*+?^${}()|[\]\\]/g, "\\$&");

    const stopSections = `==\\s*Upgrading\\s*==|==\\s*Moveset\\s*==|==\\s*Passive\\s*==|==\\s*Abilities\\s*==|__TOC__`;

    const patterns = [
      // Description following a {{Quote}} template
      new RegExp(`{{Quote(?:[\\s\\S]*?)}}\\s*([\\s\\S]*?)(?:${stopSections})`, "i"),
      // Description starting right after the bolded name introduction line
      new RegExp(`'''${escapedName}'''[\\s\\S]*?\\.\\s*([\\s\\S]*?)(?:${stopSections})`, "i")
    ];

    for (const pattern of patterns) {
      const match = wikitext.match(pattern);
      if (match && match[1]) {
        let description = match[1];

        // Remove the {{Quote|...}} template if it's at the beginning
        description = description.replace(/^{{Quote[\s\S]*?}}\s*/, '');

        // Stop description at the first major section header or TOC
        const stopIndex = description.search(/(\n\s*==[^=]+==|__TOC__)/);
        if (stopIndex !== -1) {
          description = description.substring(0, stopIndex);
        }

        const cleaned = this.cleanWikitext(description)
          .replace(/\[\[|\]\]/g, '') // Clean up any remaining link brackets
          .trim();

        if (cleaned.length > 20) {
          return cleaned;
        }
      }
    }

    return "";
  }

  // AMÉLIORÉ: Extraction des capacités passives
  private extractPassiveAbilities(wikitext: string): Array<{ name: string; description: string; showcaseUrl?: string }> {
    const abilities: Array<{ name: string; description: string; showcaseUrl?: string }> = [];
    // Capture de tout après "Passives=" jusqu'au prochain délimiteur de table ("|-" sur une nouvelle ligne) ou le début du prochain onglet (ex: "Moveset=")
    // L'ancien regex échouait lorsque le premier délimiteur de ligne "|-" était précédé d'un saut de ligne, résultant en une correspondance vide.
    const passiveSectionMatch = wikitext.match(/Passives?=\s*([\s\S]*?)(?=\n\|-\|Moveset=|\n==|<\/tabber>|$)/i);
    if (!passiveSectionMatch) return abilities;

    const passiveContent = passiveSectionMatch[1];
    
    // Diviser les lignes sur le délimiteur de ligne standard "|-" (précédé d'un saut de ligne). Garder toutes les lignes, y compris la première.
    const abilityRows = passiveContent.split(/\n\|-/).filter(r => r.trim().length > 0);

    let currentAbilityName: string | null = null;

    abilityRows.forEach(row => {
        // Ignore header rows of the fandom table
        if (/!Name/i.test(row) || /fandom-table/i.test(row)) {
            return;
        }
        const cells = this.splitTableRowSafe(row); // Utiliser le split sécurisé

        // Détermination du nom : la première cellule peut contenir le nom (souvent avec <center>''') ou être vide si rowspan
        let nameCandidate = cells.length > 1 ? this.cleanWikitext(cells[1]).trim() : "";

        // Retirer les indications rowspan="X"
        nameCandidate = nameCandidate.replace(/rowspan\s*=\s*"?\d+"?/i, '').trim();

        // Tentative de trouver un nom en gras '''Name'''
        if (!nameCandidate) {
            const boldMatch = row.match(/'''([^']+)'''/);
            if (boldMatch) {
                nameCandidate = this.cleanWikitext(boldMatch[1]).trim();
            }
        }

        // Garder uniquement la première partie avant une liste ou un saut de ligne
        if (nameCandidate.includes('\n')) {
            nameCandidate = nameCandidate.split('\n')[0].trim();
        }
        if (nameCandidate.includes('*')) {
            nameCandidate = nameCandidate.split('*')[0].trim();
        }
        // Nettoyer les puces / balises restantes
        nameCandidate = nameCandidate.replace(/^\*+/, '').trim();

        // Définir currentAbilityName si le candidat est valable
        if (nameCandidate && nameCandidate.length > 2 && !/^="?\d/.test(nameCandidate)) {
            currentAbilityName = nameCandidate;
        }

        if (!currentAbilityName) return; // Pas encore de nom valide : on ignore la ligne

        // L'index de la description dépend de la présence d'un nom explicite
        const descIndex = (nameCandidate && nameCandidate.length > 0) ? (cells.length >= 3 ? 2 : 1) : 1;

        let rawDesc = cells[descIndex] ?? "";
        let showcaseUrl: string | undefined = undefined;

        const fileInDesc = rawDesc.match(/\[\[File:([^|\]]+)/i);
        if (fileInDesc) {
            showcaseUrl = fileInDesc[1].trim();
            rawDesc = rawDesc.split("[[File:")[0]; // Remove everything from the file link onwards
        }

        // Fallback: if showcaseUrl not found in description, check the next cell (commonly used for showcase images)
        if (!showcaseUrl && cells.length > 3) {
            const fileMatch = cells[3].match(/\[\[File:([^|\]]+)/);
            if (fileMatch) {
                showcaseUrl = fileMatch[1].trim();
            }
        }

        let description = this.cleanWikitext(rawDesc).trim();
        // Remove any trailing pipe left from table parsing
        description = description.replace(/\|+\s*$/, "");
        // Enlever les crochets wiki restants si le lien était coupé
        description = description.replace(/\[\[|\]\]/g, "");
        // Normaliser les espaces
        description = description.replace(/\s+/g, " ").trim();

        // Écarter les descriptions vides ou en-têtes résiduels
        if (description.toLowerCase().startsWith('description')) return;

        const name = (currentAbilityName ?? "");

        if (name && description) {
            // Éviter d'ajouter des en-têtes de tableau
            if (name.toLowerCase() !== 'name' && description.toLowerCase() !== 'description') {
                const ability = { name, description, showcaseUrl };
                
                // Si une capacité avec le même nom existe déjà, fusionner les descriptions
                const existingAbility = abilities.find(a => a.name === name);
                if (existingAbility) {
                    existingAbility.description += `\n${description}`;
                    if (showcaseUrl && !existingAbility.showcaseUrl) {
                        existingAbility.showcaseUrl = showcaseUrl;
                    }
                } else {
                    abilities.push(ability);
                }
            }
        }
    });

    // Fusionner les items portant exactement le même nom
    const merged: typeof abilities = [];
    abilities.forEach(ab => {
        const existing = merged.find(x => x.name === ab.name);
        if (existing) {
            if (!existing.description.includes(ab.description)) {
                existing.description += '\n' + ab.description;
            }
            if (ab.showcaseUrl && !existing.showcaseUrl) existing.showcaseUrl = ab.showcaseUrl;
        } else {
            merged.push({ ...ab });
        }
    });

    // Cas particulier : si une entrée a une description très courte (<= name) on tente d'aller récupérer les puces qui la suivent dans le wikitext
    merged.forEach(ab => {
        if (ab.description.length <= ab.name.length + 2) {
            const pattern = new RegExp(`'''${ab.name.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}'''([\\s\\S]*?)(?=\n\\|-|$)`, 'i');
            const m = passiveContent.match(pattern);
            if (m) {
                const block = this.cleanWikitext(m[1]).trim();
                const bullets = block.split('*').map(s => s.trim()).filter(Boolean);
                if (bullets.length > 0) {
                    ab.description = bullets.join(' ');
                }
            }
        }
    });

    return merged;
  }

  // NOUVEAU: Extraction des formes et mouvements
  private extractForms(wikitext: string, infoboxData: Record<string, string>): FruitForm[] {
    const forms: FruitForm[] = [];

    // 1. Chercher la section Moveset ou Abilities, sinon fallback sur tout le wikitext
    let movesetSection = this.extractFromWikiSection(wikitext, 'Moveset') || this.extractFromWikiSection(wikitext, 'Abilities');
    if (!movesetSection) movesetSection = wikitext;

    // 2. Détecter s'il y a un tabber (multi-formes) dans le wikitext complet
    const hasTabber = wikitext.includes("{{#tag:tabber|") || wikitext.includes("<tabber>");
    if (hasTabber) {
      // --- LOGIQUE MULTI-FORMES (Dragon, etc.) ou SINGLE-FORME avec tabber (Gravity) ---
      let tabberContent = this.findMovesTabber(wikitext) || wikitext;

      // Parse tabber for forms - utiliser le wikitext complet pour éviter la troncature
      const formTabs = this.parseTabber(wikitext); // Parse le tabber complet

      // Filtrer les onglets valides (formes)
      const validFormTabKeys = Object.keys(formTabs).filter(formName =>
        this.isValidFormName(formName.trim()) ||
        formName.trim().toLowerCase() === 'moveset' ||
        (formName.trim().toLowerCase().includes('moveset') && formName.trim().toLowerCase().includes('transformed'))
      );

      // Si aucun onglet valide, ou un seul onglet générique ("Moveset", "Normal"), fallback sur moveset unique
      if (
        validFormTabKeys.length === 0 ||
        (validFormTabKeys.length === 1 && ['moveset', 'normal'].includes(validFormTabKeys[0].trim().toLowerCase()))
      ) {
        // Fallback sur moveset unique (Gravity) - utiliser le contenu du tabber "Moveset" directement
        let movesetContent = movesetSection;
        
        // Si on a un tabber avec un onglet "Moveset", extraire son contenu
        if (Object.keys(formTabs).includes('Moveset')) {
          movesetContent = formTabs['Moveset'];
        } else if (validFormTabKeys.length === 1) {
          movesetContent = formTabs[validFormTabKeys[0]];
        }
        
        // Extract stats first - check both standalone Stats section and tabber Stats tab
        let statsSection = this.extractFromWikiSection(wikitext, 'Stats');
        
        // If no standalone Stats section, check if Stats is in the tabber
        if (!statsSection && Object.keys(formTabs).includes('Stats')) {
          statsSection = formTabs['Stats'];
        }
        
        const topLevelStats = statsSection ? this.extractStatsFromStatsTable(statsSection) : {};
        if (Object.keys(topLevelStats).length > 0) {
          this.rawData.statsFound += Object.keys(topLevelStats).length;
        }
        
        const skillBoxes = movesetContent.match(/\{\{SkillBox[\s\S]*?\}\}/g) || [];
        if (skillBoxes.length > 0) {
          const moves = skillBoxes
            .map(box => this.parseSkillBox(box, topLevelStats)) // Pass stats to parseSkillBox
            .filter((move): move is NonNullable<ReturnType<typeof this.parseSkillBox>> => move !== null);
          this.rawData.movesFound += moves.length;
          
          forms.push({
            name: "Normal",
            type: "Normal",
            moves,
            images: this.extractFormImages("Normal", infoboxData)
          });
        }
        return forms;
      }

      // --- Cas multi-formes classique (Dragon, etc.) ---
      // Extraction du tabber "Stats" pour mapping avancé des stats par forme/move
      let statsTabberContent = this.extractFromWikiSection(wikitext, 'Stats'); // Recherche de la section Stats
      if (!statsTabberContent || (!statsTabberContent.includes("{{#tag:tabber|") && !statsTabberContent.includes("<tabber>"))) {
        let startIdx = 0;
        while (true) {
          startIdx = wikitext.indexOf("{{#tag:tabber|", startIdx);
          if (startIdx === -1) break;
          let braceCount = 0;
          let endIdx = startIdx;
          let i = startIdx;
          while (i < wikitext.length) {
            if (wikitext.substring(i, i + 2) === '{{') {
              braceCount++;
              i += 2;
            } else if (wikitext.substring(i, i + 2) === '}}') {
              braceCount--;
              i += 2;
              if (braceCount === 0) {
                endIdx = i;
                break;
              }
            } else {
              i++;
            }
          }
          if (endIdx > startIdx) {
            const candidateTabber = wikitext.substring(startIdx, endIdx);
            if (candidateTabber.includes("{{Stats Table") && !candidateTabber.includes("{{SkillBox")) {
              statsTabberContent = candidateTabber;
              break;
            }
          }
          startIdx += 14;
        }
        if (!statsTabberContent) {
          const classicTabberMatch = wikitext.match(/<tabber>([\s\S]*?)<\/tabber>/i);
          if (classicTabberMatch) {
            const candidateTabber = classicTabberMatch[0];
            if (candidateTabber.includes("{{Stats Table") && !candidateTabber.includes("{{SkillBox")) {
              statsTabberContent = candidateTabber;
            }
          }
        }
      }

      // Construction du mapping {forme: {move: stats}}
      const statsByForm: Record<string, Record<string, any>> = {};
      if (statsTabberContent) {
        const statsTabs = this.parseTabber(statsTabberContent); // Parse le tabber des stats
        for (const statsFormName in statsTabs) {
          const statsSection = statsTabs[statsFormName];
          const rows = statsSection.match(/\{\{Stats Table Row\|([^}]*)\}\}/g);
          if (rows) {
            statsByForm[statsFormName.trim()] = {}; // Initialiser l'objet pour la forme
            for (const row of rows) {
              const parts = row.match(/\{\{Stats Table Row\|([^}]*)\}\}/);
              if (!parts) continue;
              const fields = parts[1].split('|').map(x => x.trim());
              if (fields.length < 3) continue;
              const key = fields[0];
              const moveName = fields[1];
              // Associer les stats au nom du mouvement
              statsByForm[statsFormName.trim()][moveName] = {
                damage: fields[2] && fields[2] !== '?' ? fields[2] : undefined,
                cooldown: fields[3] && fields[3] !== '?' ? fields[3] : undefined,
                energy: fields[4] && fields[4] !== '?' ? fields[4] : undefined,
                furyMeter: fields[5] && fields[5] !== '?' ? fields[5] : undefined // Ajout de l'extraction du Fury Meter
              };
            }
          }
        }
      }

      const statsFromSameTabber = formTabs['Stats'] ? this.extractStatsFromStatsSection(formTabs['Stats']) : {};
      for (const formName in formTabs) {
        if (!statsByForm[formName]) {
          const formStats = this.extractStatsFromStatsSection(formTabs[formName]);
          if (Object.keys(formStats).length > 0) {
            statsByForm[formName] = formStats;
          }
        }
      }
      for (const formName of validFormTabKeys) {
        const cleanFormName = formName.trim();
        let actualFormName = cleanFormName;
        if (cleanFormName.toLowerCase() === 'moveset') {
          actualFormName = 'Normal';
        } else if (cleanFormName.toLowerCase().includes('moveset') && cleanFormName.toLowerCase().includes('transformed')) {
          actualFormName = 'Transformed';
        }
        const formSection = formTabs[formName];
        // Tenter de faire correspondre les noms de forme (ex: "Transformed (East)" avec "Transformed (East)")
        const statsKey = Object.keys(statsByForm).find(key => key.toLowerCase() === cleanFormName.toLowerCase()) || cleanFormName;
        let statsForThisForm = statsByForm[statsKey] || {};
        if (Object.keys(statsForThisForm).length === 0 && Object.keys(statsFromSameTabber).length > 0) {
          statsForThisForm = statsFromSameTabber;
        }
        // Passer les stats extraites à la fonction d'extraction des mouvements
        const moves = this.extractMovesFromSection(formSection, statsForThisForm); 
        const uniqueMoves = moves.filter(
          (move, idx, arr) =>
            arr.findIndex(
              m => m.key === move.key && m.name === move.name
            ) === idx
        );
        this.rawData.movesFound += uniqueMoves.length;
        const images = this.extractFormImages(actualFormName, infoboxData);
        forms.push({
          name: actualFormName,
          type:
            /hybrid/i.test(actualFormName)
              ? 'Hybrid'
              : /east/i.test(actualFormName)
              ? 'Transformed'
              : /west/i.test(actualFormName)
              ? 'Transformed'
              : 'Normal',
          moves: uniqueMoves,
          images
        });
      }
      return forms;
    }

    // --- LOGIQUE MOVES UNIQUE (Gravity, etc.) ---
    // Recherche d'une section de stats de haut niveau pour les fruits comme Gravity
    const topLevelStatsSection = this.extractFromWikiSection(wikitext, 'Stats');
    const topLevelStats = topLevelStatsSection ? this.extractStatsFromStatsTable(topLevelStatsSection) : {};

    // 2. Chercher tous les SkillBox dans cette section
    const skillBoxes = movesetSection.match(/\{\{SkillBox[\s\S]*?\}\}/g) || [];
    if (skillBoxes.length > 0) {
      const moves = skillBoxes
        .map(box => this.parseSkillBox(box, topLevelStats)) // Passer les stats de haut niveau
        .filter((move): move is NonNullable<ReturnType<typeof this.parseSkillBox>> => move !== null);
      this.rawData.movesFound += moves.length;
      forms.push({
        name: "Normal",
        type: "Normal",
        moves,
        images: this.extractFormImages("Normal", infoboxData)
      });
    }

    return forms;
  }

  // NOUVEAU: Validation des noms de formes
  private isValidFormName(formName: string): boolean {
    const name = formName.toLowerCase().trim();
    
    // Formes invalides à exclure
    const invalidForms = [
      'pros', 'cons', 'passive', 'passives', 'stats', 'overview',
      'general', 'description', 'showcase', 'gallery', 'trivia'
    ];
    
    // Vérifier si c'est un nom invalide
    if (invalidForms.includes(name)) {
      return false;
    }
    
    // Vérifier si c'est trop court ou contient des caractères suspects
    if (name.length < 3 || name.includes('=') || name.includes('|')) {
      return false;
    }
    
    // Formes valides connues
    const validFormPatterns = [
      /^normal$/i,
      /^hybrid/i,
      /^transformed/i,
      /^east/i,
      /^west/i,
      /^awakened/i,
      /^unawakened/i,
      /form$/i,
      /hybrid.form/i,  // "Hybrid Form"
      /transformed.*east/i,  // "Transformed (East)"
      /transformed.*west/i,   // "Transformed (West)"
      /^moveset$/i,  // "Moveset"
      /moveset.*transformed/i  // "Moveset (Transformed)"
    ];
    
    // Si ça match un pattern valide, accepter
    return validFormPatterns.some(pattern => pattern.test(name));
  }

  private extractFormFromSection(
      formName: string,
      section: string,
      infoboxData: Record<string, string>,
      statsForForm?: Record<string, any>
  ): FruitForm | null {
    if (!section) return null;

    const form: FruitForm = {
        name: formName,
        type: formName.includes('Transformed') ? 'Transformed' : formName.includes('Hybrid') ? 'Hybrid' : 'Normal',
        moves: this.extractMovesFromSection(section, statsForForm),
        images: this.extractFormImages(formName, infoboxData)
    };

    return form;
  }

  private extractMovesFromSection(section: string, statsForForm?: Record<string, any>): FruitForm['moves'] {
    const moves: FruitForm['moves'] = [];
    
    // Méthode 1: Chercher le bloc entre {{SkillStart}} et {{SkillEnd}}
    const skillBlockMatch = section.match(/\{\{SkillStart\}\}([\s\S]*?)\{\{SkillEnd\}\}/i);
    let skillBlock = '';
    
    if (skillBlockMatch) {
      skillBlock = skillBlockMatch[1];
    } else {
      // Méthode 2: Si pas de SkillStart/SkillEnd, utiliser toute la section
      skillBlock = section;
    }
    
    // Extraire tous les SkillBox de la section
    const skillBoxMatches = skillBlock.match(/\{\{SkillBox[\s\S]*?\}\}/g);

    if (skillBoxMatches) {
      for (const box of skillBoxMatches) {
        const move = this.parseSkillBox(box, statsForForm);
        if (move) {
          moves.push(move);
        }
      }
    }
    
    return moves;
  }

  private parseSkillBox(skillContent: string, statsForForm?: Record<string, any>): FruitForm['moves'][0] | null {
    const keyMatch = skillContent.match(/\{\{SkillBox\|([^|\n]+)/)
    const moveMatch = skillContent.match(/\|Move\s*=\s*([^|\n}]+)/)
    const descMatch = skillContent.match(/\|Desc\s*=\s*([\s\S]*?)(?=\n\|[A-Z]|\}\})/)
    const masMatch = skillContent.match(/\|Mas\s*=\s*(\d+)/)
    const gifMatch = skillContent.match(/\|GIF\s*=\s*([^|\n}]+)/)
    
    // Extract additional GIF variants (GIF1, GIF2)
    const gif1Match = skillContent.match(/\|GIF1\s*=\s*([^|\n}]+)/)
    const gif2Match = skillContent.match(/\|GIF2\s*=\s*([^|\n}]+)/)

    if (!keyMatch || !moveMatch) return null

    const key = this.cleanWikitext(keyMatch[1]).trim()
    const name = this.cleanWikitext(moveMatch[1]).trim()
    const description = descMatch ? this.cleanWikitext(descMatch[1]).trim() : ''
    const mastery = masMatch ? parseInt(masMatch[1]) : undefined
    const gif = gifMatch ? this.cleanWikitext(gifMatch[1]).trim() : undefined

    const move: FruitForm['moves'][0] = {
        key,
        name,
        description,
        mastery,
        gif
    }

    // Add additional GIF variants if present
    if (gif1Match) {
      move.gif1 = this.cleanWikitext(gif1Match[1]).trim();
    }
    if (gif2Match) {
      move.gif2 = this.cleanWikitext(gif2Match[1]).trim();
    }

    // Enhanced stats correlation with multiple key matching strategies
    const moveKeys = [
      key === 'TAP' ? 'Normal Attack' : name,
      name,
      key,
      key === 'TAP' ? 'TAP' : undefined
    ].filter((k): k is string => Boolean(k));

    let moveStats = undefined;
    for (const moveKey of moveKeys) {
      if (statsForForm && moveKey && statsForForm[moveKey]) {
        moveStats = statsForForm[moveKey];
        break;
      }
    }

    if (moveStats) {
        // Clean and validate stats
        // Si la valeur est un objet (range, conditional, etc.), la garder telle quelle
        move.damage = moveStats.damage !== undefined && moveStats.damage !== '?' ? moveStats.damage : undefined;
        move.cooldown = moveStats.cooldown !== undefined && moveStats.cooldown !== '?' ? moveStats.cooldown : undefined;
        move.energy = moveStats.energy !== undefined && moveStats.energy !== '?' ? moveStats.energy : undefined;
        move.furyMeter = moveStats.furyMeter !== undefined && moveStats.furyMeter !== '?' ? moveStats.furyMeter : undefined;
        // Pour chaque stat, ajouter aussi la valeur brute si c'est un objet complexe
        if (typeof moveStats.damage === "object" && moveStats.damage !== null) move.damageRaw = moveStats.damage.display || "";
        if (typeof moveStats.cooldown === "object" && moveStats.cooldown !== null) move.cooldownRaw = moveStats.cooldown.display || "";
        if (typeof moveStats.energy === "object" && moveStats.energy !== null) move.energyRaw = moveStats.energy.display || "";
        if (typeof moveStats.furyMeter === "object" && moveStats.furyMeter !== null) move.furyMeterRaw = moveStats.furyMeter.display || "";
        this.rawData.statsFound++;
    }

    return move;
  }

  private extractStatsFromStatsTable(tableContent: string): Record<string, any> {
    const stats: Record<string, any> = {};
    const rowPattern = /\{\{Stats Table Row\|([^}]*)\}\}/g;
    let match;

    while ((match = rowPattern.exec(tableContent)) !== null) {
        const rowContent = match[1];
        const parts = rowContent.split('|').map(part => part.trim());
        
        if (parts.length >= 3) {
            const moveName = this.cleanWikitext(parts[1] || "");
            if (moveName) {
                stats[moveName] = {
                    damage: this.cleanWikitext(parts[2] || "") || undefined,
                    cooldown: this.cleanWikitext(parts[3] || "") || undefined,
                    energy: this.cleanWikitext(parts[4] || "") || undefined,
                    furyMeter: this.cleanWikitext(parts[5] || "") || undefined
                };
            }
        }
    }
    return stats;
  }

  // NOUVEAU: Extraction des stats depuis une section Stats dans le même tabber
  private extractStatsFromStatsSection(statsSection: string): Record<string, any> {
    const stats: Record<string, any> = {};
    
    // Chercher tous les Stats Table Row dans la section
    const rowPattern = /\{\{Stats Table Row\|([^}]*)\}\}/g;
    let match;

    while ((match = rowPattern.exec(statsSection)) !== null) {
        const rowContent = match[1];
        const parts = rowContent.split('|').map(part => part.trim());
        
        if (parts.length >= 3) {
            const key = parts[0] || "";
            const moveName = this.cleanWikitext(parts[1] || "");
            if (moveName) {
                stats[moveName] = {
                    damage: parts[2] && parts[2] !== '?' ? this.cleanWikitext(parts[2]) : undefined,
                    cooldown: parts[3] && parts[3] !== '?' ? this.cleanWikitext(parts[3]) : undefined,
                    energy: parts[4] && parts[4] !== '?' ? this.cleanWikitext(parts[4]) : undefined,
                    furyMeter: parts[5] && parts[5] !== '?' ? this.cleanWikitext(parts[5]) : undefined
                };
            }
        }
    }
    
    return stats;
  }

  // NOUVEAU: Extraction des skins
  private extractSkins(wikitext: string): FruitData['skins'] {
    const skins: FruitData['skins'] = []

    // Extraire les informations sur les skins depuis les passives - fixed regex
    const skinMatch = wikitext.match(/\|<center>'''Skins'''<\/center>\s*\n\s*\|([\s\S]*?)(?=\|-|\|\})/i)
    if (skinMatch) {
      const skinContent = skinMatch[1]

      // Skin par défaut
      if (skinContent.includes("Green") && skinContent.includes("default")) {
        skins.push({
          name: "Green",
          type: "Default"
        })
      }

      // Skins craftables
      const craftableMatch = skinContent.match(/\{\{Skin\|([^}]+)\}\}.*?craft/gi)
      if (craftableMatch) {
        craftableMatch.forEach(match => {
          const skinName = match.match(/\{\{Skin\|([^}]+)\}\}/)?.[1]
          if (skinName && !skins.find(s => s.name === skinName)) {
            skins.push({
              name: skinName,
              type: "Craftable"
            })
          }
        })
      }

      // Skins chromatiques
      const chromaticMatch = skinContent.match(/\{\{Skin\|([^}]+)\}\}.*?chromatic/gi)
      if (chromaticMatch) {
        chromaticMatch.forEach(match => {
          const skinName = match.match(/\{\{Skin\|([^}]+)\}\}/)?.[1]
          if (skinName && !skins.find(s => s.name === skinName)) {
            skins.push({
              name: skinName,
              type: "Chromatic"
            })
          }
        })
      }

      // Extraire les prix des skins
      const priceMatches = skinContent.match(/\{\{Robux\|([0-9,]+)\}\}/g)
      if (priceMatches) {
        priceMatches.forEach((priceMatch, index) => {
          const price = this.parseNumber(priceMatch.match(/\{\{Robux\|([0-9,]+)\}\}/)?.[1] || "0")
          if (price && skins[index]) {
            skins[index].price = price
          }
        })
      }
    }

    return skins
  }

  // NOUVEAU: Extraction de la galerie
  private extractGallery(wikitext: string): Array<{ url: string; caption: string }> {
    const gallery: Array<{ url: string; caption: string }> = [];
    const gallerySection = this.extractFromWikiSection(wikitext, 'Gallery');
    if (!gallerySection) return gallery;

    const imageRegex = /([^|\n]+\.(png|jpeg|jpg|gif))\|(.*?)(?=\n|\|)/gi;
    let imageMatch;
    const imageMatches = [];
    while ((imageMatch = imageRegex.exec(gallerySection)) !== null) {
      imageMatches.push(imageMatch);
    }

    for (const match of imageMatches) {
        const url = match[1].trim();
        const caption = this.cleanWikitext(match[3]).trim();
        if (url && caption) {
            gallery.push({ url, caption });
        }
    }

    return gallery;
  }

  // NOUVEAU: Extraction des variantes (East/West)
  private extractVariants(wikitext: string): FruitData['variants'] {
    const variants: FruitData['variants'] = []

    // Détecter si le fruit a des variantes
    const hasEastVariant = wikitext.includes("Eastern Dragon") || wikitext.includes("Transformed (East)")
    const hasWestVariant = wikitext.includes("Western Dragon") || wikitext.includes("Transformed (West)")

    if (hasEastVariant) {
      const eastVariant = {
        name: "Eastern Dragon",
        type: "East" as const,
        mountingCapacity: this.extractMountingCapacity(wikitext, "Eastern"),
        flightSpeed: this.extractFlightSpeed(wikitext, "Eastern"),
        damageResistance: this.extractDamageResistance(wikitext),
        specialFeatures: this.extractSpecialFeatures(wikitext, "Eastern")
      }
      variants.push(eastVariant)
    }

    if (hasWestVariant) {
      const westVariant = {
        name: "Western Dragon",
        type: "West" as const,
        mountingCapacity: this.extractMountingCapacity(wikitext, "Western"),
        flightSpeed: this.extractFlightSpeed(wikitext, "Western"),
        damageResistance: this.extractDamageResistance(wikitext),
        specialFeatures: this.extractSpecialFeatures(wikitext, "Western")
      }
      variants.push(westVariant)
    }

    return variants
  }

  // NOUVEAU: Extraction de la capacité de montage
  private extractMountingCapacity(wikitext: string, variant: string): number | undefined {
    // Pattern: "Maximum is 4 for Eastern Form and 5 for Western Form"
    const mountingPattern = /Maximum is (\d+) for Eastern Form and (\d+) for Western Form/i
    const match = wikitext.match(mountingPattern)
    
    if (match) {
      return variant === "Eastern" ? parseInt(match[1]) : parseInt(match[2])
    }
    
    return undefined
  }

  // NOUVEAU: Extraction de la vitesse de vol
  private extractFlightSpeed(wikitext: string, variant: string): "Fastest" | "Fast" | "Average" | "Slow" | undefined {
    if (variant === "Western" && /fastest flight speed/i.test(wikitext)) {
      return "Fastest";
    }

    if (/fast flight|high speed/i.test(wikitext)) {
      return "Fast";
    }

    return undefined;
  }

  // NOUVEAU: Extraction de la résistance aux dégâts
  private extractDamageResistance(wikitext: string): string | undefined {
    const resistanceRegex = /(\d{1,3})%[^%]{0,120}?damage resistance/gi;
    let resistanceMatch;
    const matches = [];
    while ((resistanceMatch = resistanceRegex.exec(wikitext)) !== null) {
      matches.push(resistanceMatch);
    }
    if (matches.length === 0) return undefined;

    const nums = matches.map(m => parseInt(m[1])).filter(n => !isNaN(n));
    if (nums.length === 0) return undefined;

    const max = Math.max(...nums);
    return max + "%";
  }

  // NOUVEAU: Extraction des caractéristiques spéciales
  private extractSpecialFeatures(wikitext: string, variant: string): string[] {
    const features: string[] = []
    
    // Caractéristiques communes
    if (wikitext.includes("able to fly")) {
      features.push("Flight capability")
    }
    
    if (wikitext.includes("mounted by other allied players")) {
      features.push("Player mounting")
    }
    
    if (wikitext.includes("damage resistance")) {
      features.push("Damage resistance")
    }
    
    // Caractéristiques spécifiques aux variantes
    if (variant === "Eastern") {
      if (wikitext.includes("Eastern Dragon; resembling those from the East Asian culture")) {
        features.push("East Asian dragon design")
      }
      if (wikitext.includes("spinning") || wikitext.includes("spin")) {
        features.push("Spinning attacks")
      }
    }
    
    if (variant === "Western") {
      if (wikitext.includes("Western Dragon; resembling a medieval armored Dragon")) {
        features.push("Medieval armored dragon design")
      }
      if (wikitext.includes("Fastest flight speed")) {
        features.push("Fastest flight speed")
      }
      if (wikitext.includes("claws")) {
        features.push("Claw attacks")
      }
    }
    
    return features
  }

  // NOUVEAU: Extraction complète des données de prix
  private extractPriceData(wikitext: string, infoboxData: Record<string, string>): PriceData {
    const priceData: PriceData = {
      current: {}
    };

    // Prix actuels depuis l'infobox
    this.extractCurrentPrices(wikitext, infoboxData, priceData);
    
    // Prix historiques
    const historical = this.extractPriceHistory(wikitext);
    if (historical && historical.length > 0) {
      priceData.historical = historical;
    }

    // Remises et promotions
    const discounts = this.extractDiscountInfo(wikitext);
    if (discounts && discounts.length > 0) {
      priceData.discounts = discounts;
    }

    // Sources d'obtention
    const sources = this.extractPriceSources(wikitext);
    if (sources.length > 0) {
      priceData.sources = sources;
    }

    return priceData;
  }

  // NOUVEAU: Extraction des prix actuels
  private extractCurrentPrices(wikitext: string, infoboxData: Record<string, string>, priceData: PriceData): void {
    // Extraction argent
    if (infoboxData.money || infoboxData.value || infoboxData.price) {
      const valueStr = infoboxData.money || infoboxData.value || infoboxData.price;
      
      // Vérifier si c'est un statut spécial
      if (valueStr.includes("IN PROGRESS")) {
        priceData.current.status = "IN_PROGRESS";
      } else if (valueStr.includes("UNAVAILABLE")) {
        priceData.current.status = "UNAVAILABLE";
      } else {
        const value = this.parseNumber(valueStr);
        if (value && this.validateMoneyPrice(value)) {
          priceData.current.money = value;
          priceData.current.status = "AVAILABLE";
        }
      }
    }

    // Extraction Robux
    if (infoboxData.robux || infoboxData.robux_price || infoboxData.robuxprice) {
      const robuxStr = infoboxData.robux || infoboxData.robux_price || infoboxData.robuxprice;
      const robux = this.parseNumber(robuxStr);
      if (robux && this.validateRobuxPrice(robux)) {
        priceData.current.robux = robux;
      }
    }

    // Patterns additionnels dans le texte
    this.extractPricesFromText(wikitext, priceData);
  }


  // NOUVEAU: Extraction des prix historiques
  private extractPriceHistory(wikitext: string): PriceData['historical'] {
    const historical: Array<{
      money?: number
      robux?: number
      update?: string
      context?: string
    }> = [];

    // Pattern: "going from {{Money|3,500,000}} to {{Money|15,000,000}}"
    const priceChangePattern = /going from \{\{Money\|([0-9,]+)\}\} to \{\{Money\|([0-9,]+)\}\}/gi;
    let match;
    while ((match = priceChangePattern.exec(wikitext)) !== null) {
      const oldPrice = this.parseNumber(match[1]);
      const newPrice = this.parseNumber(match[2]);
      if (oldPrice && newPrice) {
        historical.push({
          money: oldPrice,
          context: `Changed from ${oldPrice.toLocaleString()} to ${newPrice.toLocaleString()}`
        });
      }
    }

    // Pattern: "Pre-rework, Dragon costed {{Robux|2,600}}"
    const preReworkPattern = /Pre-rework.*?costed? \{\{Robux\|([0-9,]+)\}\}/gi;
    while ((match = preReworkPattern.exec(wikitext)) !== null) {
      const robux = this.parseNumber(match[1]);
      if (robux) {
        historical.push({
          robux: robux,
          context: "Pre-rework price"
        });
      }
    }

    // Pattern: "Before Update X, fruit was valued {{Money|Y}}"
    const beforeUpdatePattern = /Before.*?Update.*?([0-9.]+).*?valued? \{\{Money\|([0-9,]+)\}\}/gi;
    while ((match = beforeUpdatePattern.exec(wikitext)) !== null) {
      const update = match[1];
      const money = this.parseNumber(match[2]);
      if (money) {
        historical.push({
          money: money,
          update: update,
          context: `Before Update ${update}`
        });
      }
    }

    return historical;
  }

  // NOUVEAU: Extraction des remises
  private extractDiscountInfo(wikitext: string): PriceData['discounts'] {
    const discounts: Array<{
      originalPrice: number
      discountedPrice: number
      type: "money" | "robux"
      context?: string
    }> = [];

    // Pattern: "making it {{Robux|2,400}} instead of {{Robux|5,000}}"
    const discountPattern = /making it \{\{Robux\|([0-9,]+)\}\} instead of \{\{Robux\|([0-9,]+)\}\}/gi;
    let match;
    while ((match = discountPattern.exec(wikitext)) !== null) {
      const discountedPrice = this.parseNumber(match[1]);
      const originalPrice = this.parseNumber(match[2]);
      if (discountedPrice && originalPrice) {
        discounts.push({
          originalPrice: originalPrice,
          discountedPrice: discountedPrice,
          type: "robux",
          context: "Permanent Dragon Token discount"
        });
      }
    }

    // Pattern pour les remises générales
    const generalDiscountPattern = /discount.*?([0-9]+)%/gi;
    while ((match = generalDiscountPattern.exec(wikitext)) !== null) {
      const percentage = parseInt(match[1]);
      if (percentage > 0 && percentage < 100) {
        // Extraction du contexte autour de la remise
        const contextStart = Math.max(0, match.index - 50);
        const contextEnd = Math.min(wikitext.length, match.index + 100);
        const context = wikitext.substring(contextStart, contextEnd).trim();
        
        discounts.push({
          originalPrice: 0, // À calculer si nécessaire
          discountedPrice: 0,
          type: "robux",
          context: `${percentage}% discount - ${context}`
        });
      }
    }

    return discounts;
  }

  // NOUVEAU: Extraction des sources de prix
  private extractPriceSources(wikitext: string): string[] {
    const sources: string[] = [];

    // Sources communes
    const commonSources = [
      "Blox Fruit Dealer",
      "Dragon Egg",
      "Kitsune Shrine",
      "Prehistoric Island",
      "Blox Fruit Gacha",
      "Shop"
    ];

    commonSources.forEach(source => {
      if (wikitext.includes(source)) {
        sources.push(source);
      }
    });

    // Pattern pour extraire d'autres sources
    const sourcePatterns = [
      /can be obtained from.*?\[\[([^\]]+)\]\]/gi,
      /available at.*?\[\[([^\]]+)\]\]/gi,
      /found at.*?\[\[([^\]]+)\]\]/gi
    ];

    sourcePatterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(wikitext)) !== null) {
        const source = this.cleanWikitext(match[1]);
        if (source && !sources.includes(source)) {
          sources.push(source);
        }
      }
    });

    // Dédupliquer (insensible à la casse) et normaliser la casse initiale
    const finalSources: string[] = [];
    const seen = new Set<string>();
    sources.forEach(src => {
      const key = src.toLowerCase();
      if (!seen.has(key)) {
        seen.add(key);
        // Capitaliser la première lettre pour l'uniformité
        finalSources.push(src.charAt(0).toUpperCase() + src.slice(1));
      }
    });

    return finalSources;
  }

  // NOUVEAU: Validation des prix
  private validateMoneyPrice(price: number): boolean {
    return price >= 0 && price <= 50000000; // 0 à 50M
  }

  private validateRobuxPrice(price: number): boolean {
    return price >= 0 && price <= 10000; // 0 à 10K
  }

  // NOUVEAU: Extraction des prix depuis le texte
  private extractPricesFromText(wikitext: string, priceData: PriceData): void {
    // Patterns pour {{Money|X}} et {{Robux|X}}
    const moneyPattern = /\{\{Money\|([0-9,]+)\}\}/gi;
    const robuxPattern = /\{\{Robux\|([0-9,]+)\}\}/gi;

    // Si on n'a pas encore de prix, essayer d'extraire du texte
    if (!priceData.current.money) {
      const moneyMatch = moneyPattern.exec(wikitext);
      if (moneyMatch) {
        const money = this.parseNumber(moneyMatch[1]);
        if (money && this.validateMoneyPrice(money)) {
          priceData.current.money = money;
          priceData.current.status = "AVAILABLE";
        }
      }
    }

    if (!priceData.current.robux) {
      const robuxMatch = robuxPattern.exec(wikitext);
      if (robuxMatch) {
        const robux = this.parseNumber(robuxMatch[1]);
        if (robux && this.validateRobuxPrice(robux)) {
          priceData.current.robux = robux;
        }
      }
    }
  }

  private extractImageNames(wikitext: string, infoboxData: Record<string, string>): string[] {
    const imageNames: string[] = []

    if (infoboxData.image) {
      const galleryMatch = infoboxData.image.match(/<gallery>([\s\S]*?)<\/gallery>/i)
      if (galleryMatch) {
        const galleryContent = galleryMatch[1]
        const imageLines = galleryContent.split('\n').filter(line => line.trim().length > 0)

        imageLines.forEach(line => {
          const imageName = line.split('|')[0].trim()
          if (imageName && imageName.length > 0 && imageName.match(/\.(png|jpg|jpeg|gif|webp)$/i)) {
            imageNames.push(imageName)
          }
        })
      } else if (infoboxData.image.trim().length > 0 && infoboxData.image.match(/\.(png|jpg|jpeg|gif|webp)$/i)) {
        imageNames.push(infoboxData.image.trim())
      }
    }

    const imagePatterns = [
      /\[\[File:([^\]|]+\.(?:png|jpg|jpeg|gif|webp))/gi,
      /\[\[Image:([^\]|]+\.(?:png|jpg|jpeg|gif|webp))/gi,
      /([A-Za-z0-9_\-\s]+\.(?:png|jpg|jpeg|gif|webp))/gi
    ]

    imagePatterns.forEach(pattern => {
      let match
      while ((match = pattern.exec(wikitext)) !== null) {
        const imageName = match[1].trim()
        if (imageName && imageName.length > 3 && !imageNames.includes(imageName)) {
          imageNames.push(imageName)
        }
      }
    })

    return imageNames.slice(0, 10)
  }

  private async getImageUrls(imageNames: string[]): Promise<string[]> {
    if (imageNames.length === 0) return []

    try {
      const imageUrls: string[] = []

      for (let i = 0; i < imageNames.length; i += 5) {
        const batch = imageNames.slice(i, i + 5)
        const titles = batch.map(name => `File:${name.replace(/^File:/, '')}`).join('|')

        const url = `https://blox-fruits.fandom.com/api.php?action=query&titles=${encodeURIComponent(titles)}&prop=imageinfo&iiprop=url&format=json`

        const response = await fetch(url)
        if (!response.ok) continue

        const data = await response.json() as any

        if (data.query && data.query.pages) {
          Object.values(data.query.pages).forEach((page: any) => {
            if (page.imageinfo && page.imageinfo[0] && page.imageinfo[0].url) {
              imageUrls.push(page.imageinfo[0].url)
            }
          })
        }

        await new Promise(resolve => setTimeout(resolve, 200))
      }

      return imageUrls
    } catch (error) {
      console.warn("Error fetching image URLs:", error)
      return []
    }
  }

  async scrapeItem(title: string, itemType: string): Promise<ScrapedItem | null> {
    // Reset raw data counters for each item
    this.rawData = { movesFound: 0, statsFound: 0 };

    const wikitext = await this.getPageContent(title);
    if (!wikitext) {
      console.warn(`⚠️ No wikitext found for ${title}, skipping.`);
      return null;
    }

    // Prioritize the specific "Blox Fruit Infobox" template
    let infoboxData = this.extractTemplateData(wikitext, "Blox Fruit Infobox");
    // Fallback to the generic "Infobox" if the specific one is not found
    if (Object.keys(infoboxData).length === 0) {
      infoboxData = this.extractTemplateData(wikitext, "Infobox");
    }

    let fruitData = this.extractSpecificData(wikitext, infoboxData);

    // Nettoyage générique de tous les champs texte restants (crochets, doubles espaces, pipes traînants…)
    fruitData = this.sanitizeObject(fruitData) as FruitData;

    const imageNames = this.extractImageNames(wikitext, infoboxData);
    const imageUrls = await this.getImageUrls(imageNames);

    // Build the final object without redundant top-level fields.
    // All specific data is nested under `fruitData`.
    const finalItem = {
      name: this.cleanWikitext(infoboxData.name || title),
      category: itemType,
      type: "fruit", // Scraper type
      wikiUrl: `https://blox-fruits.fandom.com/wiki/${encodeURIComponent(title.replace(/ /g, "_"))}`,
      lastUpdated: new Date(),
      imageUrl: imageUrls.length > 0 ? imageUrls[0] : undefined,
      imageUrls,
      rawData: {
        infobox: infoboxData,
        wikitextLength: wikitext.length,
        movesFound: this.rawData.movesFound,
        statsFound: this.rawData.statsFound,
        extractedAt: new Date().toISOString(),
      },
      fruitData,
    };

    return finalItem as ScrapedItem;
  }

  async scrapeCategory(categoryName: string = "Blox_Fruits"): Promise<ScrapedItem[]> {
    console.log(`\n🎯 Starting to scrape fruits from category: ${categoryName}`)

    const members = await this.getCategoryMembers(categoryName)
    if (members.length === 0) {
      return []
    }

    const items: ScrapedItem[] = []
    let processed = 0

    for (const member of members) {
      try {
        const item = await this.scrapeItem(member.title, "fruit")
        if (item) {
          items.push(item)
        }
        processed++

        if (processed % 5 === 0 || processed === members.length) {
          console.log(
            `📊 Progress: ${processed}/${members.length} (${Math.round((processed / members.length) * 100)}%)`
          )
        }

        await new Promise((resolve) => setTimeout(resolve, 200))
      } catch (error) {
        console.error(`❌ Error scraping fruit ${member.title}:`, error)
      }
    }

    console.log(`✅ Completed fruits: ${items.length}/${members.length} items scraped`)
    return items
  }

  private extractOverviewItems(overviewWikitext: string): string[] {
    if (!overviewWikitext) return [];

    const match = overviewWikitext.match(/\{\{Overview\|[^|]*\|([\s\S]*?)\}\}/);
    if (!match || !match[1]) return [];

    const content = match[1];

    const potentialItems = content.split(/\n\s*(?:\|\s*)?/);

    const cleanedLines = potentialItems
       .map(line => this.cleanWikitext(line).trim())
       .filter(line => {
          const l = line.trim();
          if (l.length <= 10) return false; // Too short / likely header
          if (/type=/i.test(l)) return false; // "type=Normal" etc.
          if (/^[A-Za-z0-9_-]+=/.test(l)) return false; // "General=" like headers
          if (/\[[A-Za-z]/.test(l)) return false; // Lines starting with move tags like "[TAP]"
          if (/general/i.test(l)) return false; // Generic header word
          if (/^\[tap|\[z|\[x|\[c|\[v|\[f/i.test(l)) return false; // Explicit move tag prefix
          if (l.startsWith("'''") || l.endsWith("'''") ) return false; // Bold header lines
          return true;
       });

    // Deduplicate while preserving order (case-insensitive)
    const unique: string[] = [];
    const seen = new Set<string>();
    cleanedLines.forEach(line => {
      const key = line.toLowerCase();
      if (!seen.has(key)) {
        seen.add(key);
        unique.push(line);
      }
    });

    return unique;
  }

  private extractOverviewData(wikitext: string): Record<string, { pros: string[]; cons: string[] }> {
    const overviewData: Record<string, { pros: string[]; cons: string[] }> = {};
    const overviewSection = this.extractFromWikiSection(wikitext, "Overview"); // Extraire la section
    if (!overviewSection) return overviewData;

    const formOverviews = this.parseTabber(overviewSection); // Parser les onglets de la section (ex: Normal, Transformed (East))
    
    for (const formNameKey in formOverviews) {
        const content = formOverviews[formNameKey];
        const prosConsTabs = this.parseTabber(content);

        const prosContent = prosConsTabs['Pros'] || '';
        const consContent = prosConsTabs['Cons'] || '';
        
        const pros = this.extractOverviewItems(prosContent);
        const cons = this.extractOverviewItems(consContent);

        const formName = formNameKey.replace(/=/g, '').trim();

        if (pros.length > 0 || cons.length > 0) {
            overviewData[formName] = { pros, cons };
        }
    }

    return overviewData;
  }

  private extractReworkDetails(wikitext: string): string | null {
    const sectionContent = this.extractFromWikiSection(wikitext, "Dragon Token");
    if (!sectionContent) return null;

    // Nettoyer le contenu des templates et des liens excessifs pour ne garder que le texte.
    return this.cleanWikitext(sectionContent)
      .replace(/\{\{Main\|Dragon Token\}\}/gi, '')
      .replace(/\n+/g, '\n') // Normaliser les sauts de ligne
      .trim();
  }

  private extractFormImages(formName: string, infoboxData: Record<string, string>): FruitForm['images'] {
    const images: FruitForm['images'] = {};
    let tabNumber = 1; // Default to tab 1 for "All" or "Normal"

    if (formName.includes('East')) {
        tabNumber = 2;
    } else if (formName.includes('West')) {
        tabNumber = 3;
    }
    // Ajoutez d'autres logiques si nécessaire pour d'autres formes/tabs

    const imageData = infoboxData[`image${tabNumber}`];
    if (!imageData) return images;

    const imageLines = imageData.split('\n').filter(line => line.trim());
    if (imageLines.length > 0) images.fruit = imageLines[0].trim(); // Dragon Fruit.png
    if (imageLines.length > 1) images.icon = imageLines[1].trim(); // Dragon.png
    if (imageLines.length > 2) images.gif = imageLines[2].trim();
    
    return images;
  }

  /**
   * Parcours récursivement un objet/array et nettoie toutes les chaînes.
   */
  private sanitizeObject(value: any): any {
    if (typeof value === 'string') {
      return this.sanitizeString(value);
    }
    if (Array.isArray(value)) {
      return value.map(v => this.sanitizeObject(v));
    }
    if (value && typeof value === 'object') {
      const cleaned: Record<string, any> = {};
      Object.keys(value).forEach(key => {
        cleaned[key] = this.sanitizeObject(value[key]);
      });
      return cleaned;
    }
    return value;
  }

  /**
   * Nettoie une chaîne résiduelle : supprime crochets/accollades restants, pipes isolés et espaces multiples.
   */
  private sanitizeString(str: string): string {
    return str
      // Remove wiki link brackets and template braces
      .replace(/\[\[|\]\]/g, '')
      .replace(/\{\{|\}\}/g, '')
      // Remove leading list asterisks and pipes
      .replace(/^\*+/g, '')
      .replace(/^\|+/g, '')
      // Remove trailing pipes and standalone size artifacts like "|100px"
      .replace(/\|+\s*$/g, '')
      .replace(/\|\s*\d+px/gi, '')
      // Collapse multiple whitespace
      .replace(/\s+/g, ' ')
      .trim();
  }

  /**
   * Split a wiki table row into cells while ignoring '|' that appear inside wiki links ([[..|..]])
   * or templates ({{..|..}}). This prevents accidental truncation of texts such as
   * "34% against [[Enemies|NPCs]]".
   */
  private splitTableRowSafe(row: string): string[] {
    const cells: string[] = [];
    let current = "";
    let linkDepth = 0;
    let templateDepth = 0;

    for (let i = 0; i < row.length; i++) {
      const ch = row[i];
      const next = row[i + 1];

      // Handle link opening/closing
      if (ch === '[' && next === '[') {
        linkDepth++;
        current += '[[';
        i++; // Skip next char
        continue;
      }
      if (ch === ']' && next === ']' && linkDepth > 0) {
        linkDepth--;
        current += ']]';
        i++;
        continue;
      }

      // Handle template opening/closing
      if (ch === '{' && next === '{') {
        templateDepth++;
        current += '{{';
        i++;
        continue;
      }
      if (ch === '}' && next === '}' && templateDepth > 0) {
        templateDepth--;
        current += '}}';
        i++;
        continue;
      }

      // Split only on top-level pipes
      if (ch === '|' && linkDepth === 0 && templateDepth === 0) {
        cells.push(current.trim());
        current = "";
        continue;
      }

      current += ch;
    }

    cells.push(current.trim());
    return cells;
  }

  // NOUVEAU: Extract detailed Fury Meter mechanics
  private extractFuryMeterMechanics(wikitext: string): any {
    // Fixed regex pattern - note the double brackets [[Fury Meter]]
    const furyMeterSection = wikitext.match(/'''\[\[Fury Meter\]\]'''[\s\S]*?(?=\|-|'''[^']*'''|\n\s*\|)/i);
    if (!furyMeterSection) return null;

    const content = furyMeterSection[0];
    const mechanics: any = {};

    // Orange meter (first meter)
    if (content.includes("Orange meter") || content.includes("orange Fury Meter")) {
      mechanics.orangeMeter = {
        fillMethod: "time-based",
        requiredFor: "hybrid transformation",
        drainTriggers: []
      };

      if (content.includes("refills slowly over time")) {
        mechanics.orangeMeter.fillMethod = "time-based";
      }
      if (content.includes("drains with any move used")) {
        mechanics.orangeMeter.drainTriggers.push("move usage");
      }
    }

    // Red meter (second meter)
    if (content.includes("red Fury Meter") || content.includes("second Fury Meter")) {
      mechanics.redMeter = {
        fillMethod: "damage-based",
        requiredFor: "full transformation",
        drainTriggers: []
      };

      if (content.includes("fills only by dealing damage")) {
        mechanics.redMeter.fillMethod = "damage-based";
      }
    }

    // General mechanics
    if (content.includes("taking damage starts draining")) {
      if (mechanics.orangeMeter) mechanics.orangeMeter.drainTriggers.push("taking damage");
      if (mechanics.redMeter) mechanics.redMeter.drainTriggers.push("taking damage");
    }

    if (content.includes("doesn't drain passively while transformed")) {
      mechanics.passiveDrain = false;
      mechanics.allowsIndefiniteTransformation = true;
    }

    return Object.keys(mechanics).length > 0 ? mechanics : null;
  }

  // NOUVEAU: Extract damage resistance data by form
  private extractDamageResistanceData(wikitext: string): any {
    const resistance: any = {};

    // Extract from Draconic Blood section
    const draconicBloodMatch = wikitext.match(/'''Draconic Blood'''[\s\S]*?(?=\|-|'''[^']*'''|\n\s*\|)/i);
    if (draconicBloodMatch) {
      const content = draconicBloodMatch[0];
      
      // CORRECTIF: Regex améliorée pour capturer correctement la résistance de base et celle des PNJ.
      const hybridMatch = content.match(/(\d+)%\s*(?:\((\d+)%\s+against.*?NPCs\))?/i);
      if (hybridMatch) {
        resistance.hybrid = {
          players: hybridMatch[1] + "%",
          npcs: (hybridMatch[2] ? hybridMatch[2] : hybridMatch[1]) + "%"
        };
      }
    }

    // Extract from Draconic Monarch's Scales section
    const draconicScalesMatch = wikitext.match(/'''Draconic Monarch's Scales'''[\s\S]*?(?=\|-|'''[^']*'''|\n\s*\|)/i);
    if (draconicScalesMatch) {
      const content = draconicScalesMatch[0];
      
      // Dragon form resistance: 53%
      const dragonMatch = content.match(/(\d+)%.*?damage resistance/i);
      if (dragonMatch) {
        resistance.transformed = dragonMatch[1] + "%";
      }
    }

    // General extraction fallback
    if (Object.keys(resistance).length === 0) {
      const resistanceRegex = /(\d{1,3})%[^%]{0,120}?damage resistance/gi;
      let resistanceMatch;
      const allMatches = [];
      while ((resistanceMatch = resistanceRegex.exec(wikitext)) !== null) {
        allMatches.push(resistanceMatch);
      }
      if (allMatches.length > 0) {
        const nums = allMatches.map(m => parseInt(m[1])).filter(n => !isNaN(n));
        if (nums.length > 0) {
          // Assume highest is transformed, lower is hybrid
          const sorted = nums.sort((a, b) => b - a);
          if (sorted.length >= 2) {
            resistance.transformed = sorted[0] + "%";
            resistance.hybrid = { players: sorted[1] + "%", npcs: sorted[1] + "%" };
          } else {
            resistance.transformed = sorted[0] + "%";
          }
        }
      }
    }

    resistance.human = "0%"; // Always 0% in human form

    return Object.keys(resistance).length > 1 ? resistance : null;
  }

  // NOUVEAU: Enhanced skin system extraction
  private extractSkinSystemComplete(wikitext: string): any {
    // ... (le code existant est bon, mais l'appel doit être fait avant les passives)
    const skinSystem: any = {
      defaultSkin: "Green",
      craftableSkins: [],
      chromaticSkins: [],
      acquisition: {}
    };

    // Fixed regex pattern - account for line break structure in MediaWiki tables
    const skinSectionMatch = wikitext.match(/'''Skins'''<\/center>\s*\|([\s\S]*?)(?=\n\|-|\n\|<center>'''|\n\|\}|\n==)/i);
    if (!skinSectionMatch) return null;

    const skinContent = skinSectionMatch[1];

    // Extract default skin
    const defaultSkinMatch = skinContent.match(/\{\{Skin\|([^}]+)\}\} is unlocked and equipped by default/);
    if (defaultSkinMatch) {
      skinSystem.defaultSkin = defaultSkinMatch[1].trim();
    }
 
    // Extract craftable skins from the specific line mentioning "crafting them"
    const craftableLineMatch = skinContent.match(/The \{\{Skin\|[^.]*?crafting them/);
    if (craftableLineMatch) {
      const craftableLine = craftableLineMatch[0];
      const craftableMatches = craftableLine.match(/\{\{Skin\|([^}]+)\}\}/g);
      if (craftableMatches) {
        craftableMatches.forEach((match: string) => {
          const skinName = match.match(/\{\{Skin\|([^}]+)\}\}/)?.[1]?.trim();
          if (skinName && !skinSystem.craftableSkins.includes(skinName)) {
            skinSystem.craftableSkins.push(skinName);
          }
        });
      }
    }

    // Extract chromatic skins from the specific line mentioning "chromatic skins"
    const chromaticLineMatch = skinContent.match(/The \{\{Skin\|[^.]*?chromatic skins/);
    if (chromaticLineMatch) {
      const chromaticLine = chromaticLineMatch[0];
      const chromaticMatches = chromaticLine.match(/\{\{Skin\|([^}]+)\}\}/g);
      if (chromaticMatches) {
        chromaticMatches.forEach((match: string) => {
          const skinName = match.match(/\{\{Skin\|([^}]+)\}\}/)?.[1]?.trim();
          if (skinName && !skinSystem.chromaticSkins.includes(skinName)) {
            skinSystem.chromaticSkins.push(skinName);
          }
        });
      }
    }

    // Extract acquisition methods
    if (skinContent.includes("Barista")) {
      skinSystem.acquisition.crafting = "Barista NPC";
    }
    if (skinContent.includes("Robux")) {
      skinSystem.acquisition.purchase = "Robux (permanent required)";
    }
    if (skinContent.includes("Shop")) {
      skinSystem.acquisition.limited = "Shop bundles";
    }

    // Extract specific prices - look for Eclipse price specifically
    const eclipsePriceMatch = skinContent.match(/\{\{Skin\|Eclipse\}\}[^.]*?\{\{Robux\|([0-9,]+)\}\}/);
    if (eclipsePriceMatch) {
      if (!skinSystem.prices) skinSystem.prices = {};
      const eclipsePrice = this.parseNumber(eclipsePriceMatch[1]);
      if (eclipsePrice) {
        skinSystem.prices.Eclipse = eclipsePrice;
      }
    }

    return skinSystem.craftableSkins.length > 0 || skinSystem.chromaticSkins.length > 0 || skinSystem.defaultSkin !== "Green" ? skinSystem : null;
  }

  // NOUVEAU: Enhanced variant data extraction
  private extractVariantDataComplete(wikitext: string): any {
    const variants: any = {};

    // Eastern Dragon detection and data
    if (wikitext.includes("Eastern Dragon") || wikitext.includes("Transformed (East)")) {
      variants.eastern = {
        name: "Eastern Dragon",
        theme: "East Asian mythology",
        mountingCapacity: this.extractMountingCapacity(wikitext, "Eastern"),
        specialMechanics: [],
        movementStyle: "aerial maneuverability"
      };

      // Extract special mechanics for Eastern
      if (wikitext.includes("spinning") || wikitext.includes("spin")) {
        variants.eastern.specialMechanics.push("spinning attacks");
      }
      if (wikitext.includes("upward")) {
        variants.eastern.specialMechanics.push("upward spirals");
      }
      if (wikitext.includes("East Asian culture")) {
        variants.eastern.culturalReference = "East Asian dragon mythology";
      }
    }

    // Western Dragon detection and data
    if (wikitext.includes("Western Dragon") || wikitext.includes("Transformed (West)")) {
      variants.western = {
        name: "Western Dragon",
        theme: "European medieval",
        mountingCapacity: this.extractMountingCapacity(wikitext, "Western"),
        specialMechanics: [],
        movementStyle: "direct assault"
      };

      // Extract special mechanics for Western
      if (wikitext.includes("claw") || wikitext.includes("claws")) {
        variants.western.specialMechanics.push("claw attacks");
      }
      if (wikitext.includes("fastest flight") || wikitext.includes("Fastest flight speed")) {
        variants.western.specialMechanics.push("fastest flight speed");
        variants.western.flightSpeed = "fastest";
      }
      if (wikitext.includes("medieval armored Dragon")) {
        variants.western.culturalReference = "Medieval European dragon";
      }
    }

    return Object.keys(variants).length > 0 ? variants : null;
  }

  // NOUVEAU: Extract enhanced economic data
  private extractEconomicDataAdvanced(wikitext: string): any {
    const economicData: any = {
      acquisitionMethods: [],
      reworkHistory: {},
      competitiveRanking: {},
      marketAnalysis: {}
    };

    // Acquisition methods from various sources
    const acquisitionSources = [
      { pattern: /Dragon Egg/i, source: "Dragon Egg", location: "Prehistoric Island", chance: "extremely low" },
      { pattern: /Kitsune Shrine/i, source: "Kitsune Shrine", type: "Sea Event" },
      { pattern: /Blox Fruit Dealer/i, source: "Blox Fruit Dealer", type: "Shop" },
      { pattern: /Blox Fruit Gacha/i, source: "Blox Fruit Gacha", type: "Random" }
    ];

    acquisitionSources.forEach(({ pattern, source, location, chance, type }) => {
      if (pattern.test(wikitext)) {
        const method: any = { source };
        if (location) method.location = location;
        if (chance) method.chance = chance;
        if (type) method.type = type;
        economicData.acquisitionMethods.push(method);
      }
    });

    // Rework history analysis
    if (wikitext.includes("Dragon Token")) {
      economicData.reworkHistory.majorUpdate = "Update 24";
      economicData.reworkHistory.tokenSystem = "Dragon Token conversion";
      
      // Extract percentage increases
      const priceIncreaseRegex = /increased by ([\d.]+)%/gi;
      let priceIncreaseMatch;
      const priceIncreaseMatches = [];
      while ((priceIncreaseMatch = priceIncreaseRegex.exec(wikitext)) !== null) {
        priceIncreaseMatches.push(priceIncreaseMatch);
      }
      if (priceIncreaseMatches.length > 0) {
        economicData.reworkHistory.priceIncrease = {};
        priceIncreaseMatches.forEach(match => {
          const percentage = match[1];
          if (match[0].includes("Money") || match[0].includes("15,000,000")) {
            economicData.reworkHistory.priceIncrease.money = percentage + "%";
          }
          if (match[0].includes("Robux") || match[0].includes("92.3")) {
            economicData.reworkHistory.priceIncrease.robux = percentage + "%";
          }
        });
      }
    }

    // Competitive ranking
    if (wikitext.includes("most expensive") || wikitext.includes("highest")) {
      economicData.competitiveRanking.difficulty = "Highest in game";
    }
    if (wikitext.includes("one of the best") && wikitext.includes("grinding")) {
      economicData.competitiveRanking.grinding = "S-tier when transformed";
    }
    if (wikitext.includes("one of the best") && wikitext.includes("PvP")) {
      economicData.competitiveRanking.pvp = "S-tier when transformed";
    }

    // Market analysis
    if (wikitext.includes("most expensive")) {
      economicData.marketAnalysis.pricePosition = "Most expensive fruit";
    }
    if (wikitext.includes("difficult-to-obtain")) {
      economicData.marketAnalysis.availability = "Extremely rare";
    }

    return Object.keys(economicData.acquisitionMethods).length > 0 || 
           Object.keys(economicData.reworkHistory).length > 0 ||
           Object.keys(economicData.competitiveRanking).length > 0 ||
           Object.keys(economicData.marketAnalysis).length > 0 ? economicData : null;
  }

  // NOUVEAU: Enhanced change history extraction
  private extractChangeHistoryAdvanced(wikitext: string): any {
    const changeHistorySection = wikitext.match(/==\s*Change History\s*==([\s\S]*?)(?===|$)/i);
    if (!changeHistorySection) return null;

    const historyContent = changeHistorySection[1];
    const changes: any[] = [];

    // Extract detailed update blocks
    const updateBlocks = historyContent.match(/\{\{Update\|([^\}]+)\}\}([\s\S]*?)(?=\{\{Update\||----|\n\}\}|$)/g);
    if (!updateBlocks) return null;

    updateBlocks.forEach(block => {
      const updateMatch = block.match(/\{\{Update\|([^\}]+)\}\}/);
      if (!updateMatch) return;

      const updateNumber = updateMatch[1].trim();
      const updateData: any = {
        update: updateNumber,
        changes: [],
        type: "general"
      };

      // Determine update type
      if (block.includes("rework") || block.includes("remake")) {
        updateData.type = "major_rework";
      } else if (block.includes("Buff") || block.includes("Nerf")) {
        updateData.type = "balance";
      } else if (block.includes("released") || block.includes("Added")) {
        updateData.type = "release";
      }

      // Extract individual changes with categories
      const changeLines = block.split('\n').filter(line => line.trim().startsWith('*'));
      changeLines.forEach(line => {
        const cleanChange = this.cleanWikitext(line.replace('*', '').trim());
        if (cleanChange.length > 5) {
          const change: any = { description: cleanChange };
          
          // Categorize changes
          if (cleanChange.includes("Buff") || cleanChange.includes("increased")) {
            change.type = "buff";
          } else if (cleanChange.includes("Nerf") || cleanChange.includes("reduced") || cleanChange.includes("decreased")) {
            change.type = "nerf";
          } else if (cleanChange.includes("Add") || cleanChange.includes("New")) {
            change.type = "addition";
          } else if (cleanChange.includes("Fix") || cleanChange.includes("fix")) {
            change.type = "fix";
          } else {
            change.type = "general";
          }

          updateData.changes.push(change);
        }
      });

      if (updateData.changes.length > 0) {
        changes.push(updateData);
      }
    });

    return changes.length > 0 ? changes : null;
  }
}
