import fetch from "node-fetch"
import { RateLimiter, ScrapedItem } from "./types"
import { EnhancedStatsExtractor } from "./enhanced-stats-extractor"

export abstract class BaseScraper {
  protected baseUrl = "https://blox-fruits.fandom.com/api.php"
  protected rateLimiter: RateLimiter
  protected enhancedStatsExtractor: EnhancedStatsExtractor

  constructor() {
    this.rateLimiter = {
      requests: [],
      maxRequests: 90, // 90 requests per minute to be safe
      timeWindow: 60000, // 1 minute
    }
    this.enhancedStatsExtractor = new EnhancedStatsExtractor()
  }

  protected async waitForRateLimit(): Promise<void> {
    const now = Date.now()
    this.rateLimiter.requests = this.rateLimiter.requests.filter((time: number) => now - time < this.rateLimiter.timeWindow)

    if (this.rateLimiter.requests.length >= this.rateLimiter.maxRequests) {
      const waitTime = this.rateLimiter.timeWindow - (now - this.rateLimiter.requests[0]) + 1000 // Add 1s buffer
      console.log(`⏳ Rate limit reached, waiting ${Math.ceil(waitTime / 1000)}s...`)
      await new Promise((resolve) => setTimeout(resolve, waitTime))
    }

    this.rateLimiter.requests.push(now)
  }

  protected async makeApiRequest(params: Record<string, string>): Promise<any> {
    await this.waitForRateLimit()

    const url = `${this.baseUrl}?${new URLSearchParams(params)}`
    const response = await fetch(url)

    if (!response.ok) {
      throw new Error(`API request failed: ${response.status} ${response.statusText} for ${url}`)
    }

    return await response.json()
  }

  async getCategoryMembers(categoryName: string): Promise<Array<{ title: string; pageid: number }>> {
    console.log(`🔍 Fetching members of category: ${categoryName}`)

    const params = {
      action: "query",
      list: "categorymembers",
      cmtitle: `Category:${categoryName}`,
      cmlimit: "500", // Max limit
      format: "json",
    }

    try {
      const data = await this.makeApiRequest(params)

      if (!data.query?.categorymembers) {
        console.warn(`⚠️ No members found for category: ${categoryName}`)
        return []
      }

      const members = data.query.categorymembers
      console.log(`✅ Found ${members.length} members in ${categoryName}`)
      return members
    } catch (error) {
      console.error(`❌ Error fetching category ${categoryName}:`, error)
      return []
    }
  }

  async getPageContent(title: string): Promise<string | null> {
    const params = {
      action: "query",
      titles: title,
      prop: "revisions",
      rvprop: "content",
      format: "json",
    }

    try {
      const data = await this.makeApiRequest(params)
      const pages = data.query?.pages

      if (!pages) return null

      const pageId = Object.keys(pages)[0]
      const page = pages[pageId]

      if (page.missing || !page.revisions) {
        // Handle redirects
        const wikitextContent = page.revisions?.[0]?.["*"];
        if (wikitextContent && wikitextContent.toUpperCase().startsWith("#REDIRECT")) {
          const redirectTargetMatch = wikitextContent.match(/#REDIRECT\s*\[\[([^\]]+)\]\]/i)
          if (redirectTargetMatch && redirectTargetMatch[1]) {
            console.log(`🔄 Redirect found for "${title}", redirecting to "${redirectTargetMatch[1]}"`)
            return this.getPageContent(redirectTargetMatch[1])
          }
        }
        return null
      }

      return page.revisions[0]["*"]
    } catch (error) {
      console.error(`❌ Error fetching content for ${title}:`, error)
      return null
    }
  }

  protected extractTemplateData(wikitext: string, templateName: string): Record<string, string> {
    const data: Record<string, string> = {}
    const escapedTemplateName = templateName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')

    // Cible le bloc de l'infobox
    const templatePattern = new RegExp(`\\{\\{\\s*${escapedTemplateName}\\s*([\\s\\S]*?)\\}\\}`, 'i')
    const templateMatch = wikitext.match(templatePattern)
    if (!templateMatch || !templateMatch[1]) return data

    const templateContent = templateMatch[1]
    
    // Regex pour extraire chaque paramètre | clé = valeur
    const paramPattern = /\|\s*([^=]+?)\s*=\s*([\s\S]*?)(?=\n\s*\|[^=]+=|\s*\}\}$)/g
    let paramMatch

    while ((paramMatch = paramPattern.exec(templateContent)) !== null) {
      const key = paramMatch[1].trim().toLowerCase()
      let value = paramMatch[2].trim()
      
      // Nettoyer les commentaires et autres artefacts restants
      if (value.endsWith("-->")) {
        value = value.substring(0, value.lastIndexOf("<!--"))
      }

      if (key && value) {
        data[key] = this.cleanWikitext(value)
      }
    }

    return data
  }

  protected cleanWikitext(text: string): string {
    if (!text) return ""

    return text
      // Conserve le contenu des modèles simples comme {{Ability|C}} -> C
      .replace(/\{\{([^|}]+)\|([^}]+)\}\}/g, "$2")
      // Conserve le contenu des modèles sans pipe comme {{Mythical}} -> Mythical
      .replace(/\{\{([^}]+)\}\}/g, "$1")
      // Remove wiki links but keep the display text
      .replace(/\[\[([^|\]]+)\|([^\]]+)\]\]/g, "$2")
      .replace(/\[\[([^\]]+)\]\]/g, "$1")
      // Remove HTML tags
      .replace(/<[^>]*>/g, "")
      // Remove bold/italic markers
      .replace(/'''/g, "")
      .replace(/''/g, "")
      // Remove extra whitespace
      .replace(/\s+/g, " ")
      .trim()
  }

  protected parseNumber(str: string): number | undefined {
    if (!str) return undefined
    
    const cleaned = str.replace(/[^\d.]/g, "")
    const num = parseFloat(cleaned)
    return isNaN(num) ? undefined : num
  }

  /**
   * Enhanced method to extract detailed stats data
   */
  protected extractEnhancedStatsData(wikitext: string): any {
    return {
      statsTableData: this.enhancedStatsExtractor.extractStatsTableRows(wikitext),
      skillBoxData: this.enhancedStatsExtractor.extractSkillBoxData(wikitext),
      overviewData: this.enhancedStatsExtractor.extractOverviewData(wikitext),
      damageResistance: this.enhancedStatsExtractor.extractDamageResistance(wikitext),
      variantData: this.enhancedStatsExtractor.extractVariantData(wikitext)
    }
  }

  /**
   * Extract image URLs from wikitext content
   */
  protected extractImageUrls(wikitext: string): string[] {
    const imageUrls: string[] = []
    const baseImageUrl = "https://static.wikia.nocookie.net/roblox-blox-piece/images"
    
    // Pattern pour [[File:filename.ext|...]]
    const filePattern = /\[\[File:([^|\]]+)(?:\|[^\]]*)?\]\]/g
    let match
    
    while ((match = filePattern.exec(wikitext)) !== null) {
      const filename = match[1].trim()
      // Convert filename to Wikia URL format
      const encodedFilename = encodeURIComponent(filename.replace(/ /g, '_'))
      imageUrls.push(`${baseImageUrl}/${encodedFilename}`)
    }
    
    // Pattern pour les GIFs dans les SkillBox
    const gifPattern = /\|GIF\s*=\s*([^\n|]+)/g
    while ((match = gifPattern.exec(wikitext)) !== null) {
      const gifName = match[1].trim()
      if (gifName && !gifName.includes('|')) {
        const encodedGif = encodeURIComponent(gifName + '.gif')
        imageUrls.push(`${baseImageUrl}/${encodedGif}`)
      }
    }
    
    return [...new Set(imageUrls)] // Remove duplicates
  }

  /**
   * Extract gallery images
   */
  protected extractGalleryData(wikitext: string): Array<{ url: string; caption: string }> {
    const galleryItems: Array<{ url: string; caption: string }> = []
    
    // Pattern pour <gallery>...</gallery>
    const galleryPattern = /<gallery>([\s\S]*?)<\/gallery>/g
    let galleryMatch
    
    while ((galleryMatch = galleryPattern.exec(wikitext)) !== null) {
      const galleryContent = galleryMatch[1]
      const itemPattern = /([^|\n]+)(?:\|([^\n]+))?/g
      let itemMatch
      
      while ((itemMatch = itemPattern.exec(galleryContent)) !== null) {
        const filename = itemMatch[1].trim()
        const caption = itemMatch[2] ? itemMatch[2].trim() : filename
        
        if (filename) {
          const encodedFilename = encodeURIComponent(filename.replace(/ /g, '_'))
          galleryItems.push({
            url: `https://static.wikia.nocookie.net/roblox-blox-piece/images/${encodedFilename}`,
            caption: this.cleanWikitext(caption)
          })
        }
      }
    }
    
    return galleryItems
  }

  /**
   * Extract trivia data
   */
  protected extractTrivia(wikitext: string): string[] {
    const trivia: string[] = []
    
    const triviaPattern = /==Trivia==([\s\S]*?)(?===|$)/
    const triviaMatch = wikitext.match(triviaPattern)
    
    if (triviaMatch) {
      const triviaContent = triviaMatch[1]
      const bulletPattern = /\*\s*([^\n]+)/g
      let bulletMatch
      
      while ((bulletMatch = bulletPattern.exec(triviaContent)) !== null) {
        const item = this.cleanWikitext(bulletMatch[1])
        if (item) {
          trivia.push(item)
        }
      }
    }
    
    return trivia
  }

  // Abstract methods that must be implemented by subclasses
  abstract extractSpecificData(wikitext: string, infoboxData: Record<string, string>): any
  abstract scrapeItem(title: string, itemType: string): Promise<ScrapedItem | null>
}
