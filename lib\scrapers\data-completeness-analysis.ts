import fs from 'fs';

interface DataCompletenessReport {
  fruit: string;
  totalSections: number;
  extractedSections: number;
  completenessPercentage: number;
  missingSections: string[];
  extractedSectionsList: string[];
  detailedAnalysis: Record<string, any>;
}

async function analyzeDataCompleteness() {
  console.log('📊 Analyzing data completeness...\n');

  // Load scraped data
  const dragonScraped = JSON.parse(fs.readFileSync('lib/scrapers/Scraped.txt', 'utf-8'));
  const gravityScraped = JSON.parse(fs.readFileSync('lib/scrapers/Scraped2.txt', 'utf-8'));

  // Load raw data
  const dragonRaw = JSON.parse(fs.readFileSync('lib/scrapers/WIKITEXT.txt', 'utf-8'));
  const gravityRaw = JSON.parse(fs.readFileSync('lib/scrapers/WIKITEXT2.txt', 'utf-8'));

  const dragonWikitext = dragonRaw.query.pages[Object.keys(dragonRaw.query.pages)[0]].revisions[0]['*'];
  const gravityWikitext = gravityRaw.query.pages[Object.keys(gravityRaw.query.pages)[0]].revisions[0]['*'];

  // Analyze Dragon
  const dragonReport = analyzeFruitData('Dragon', dragonScraped, dragonWikitext);
  
  // Analyze Gravity
  const gravityReport = analyzeFruitData('Gravity', gravityScraped, gravityWikitext);

  // Print reports
  printReport(dragonReport);
  printReport(gravityReport);

  // Summary
  console.log('\n🎯 SUMMARY');
  console.log('=' .repeat(50));
  console.log(`Dragon completeness: ${dragonReport.completenessPercentage.toFixed(1)}%`);
  console.log(`Gravity completeness: ${gravityReport.completenessPercentage.toFixed(1)}%`);
  console.log(`Overall average: ${((dragonReport.completenessPercentage + gravityReport.completenessPercentage) / 2).toFixed(1)}%`);
}

function analyzeFruitData(fruitName: string, scrapedData: any, wikitext: string): DataCompletenessReport {
  const report: DataCompletenessReport = {
    fruit: fruitName,
    totalSections: 0,
    extractedSections: 0,
    completenessPercentage: 0,
    missingSections: [],
    extractedSectionsList: [],
    detailedAnalysis: {}
  };

  // Define expected sections based on wikitext analysis
  const expectedSections = [
    'Basic Info (Infobox)',
    'Price Data',
    'Stats Data',
    'Abilities/Moveset',
    'Passive Abilities',
    'Overview (Pros/Cons)',
    'Gallery',
    'Trivia',
    'Change History',
    'Upgrade Data', // Gravity specific
    'Instinct Data', // Gravity specific
    'Skin System', // Dragon specific
    'Dragon Token Info', // Dragon specific
    'Tips & Tricks'
  ];

  // Check each section
  expectedSections.forEach(section => {
    const isPresent = checkSectionPresence(section, scrapedData, wikitext);
    if (isPresent.present) {
      report.extractedSectionsList.push(section);
      report.extractedSections++;
    } else {
      report.missingSections.push(section);
    }
    report.detailedAnalysis[section] = isPresent;
    report.totalSections++;
  });

  report.completenessPercentage = (report.extractedSections / report.totalSections) * 100;

  return report;
}

function checkSectionPresence(section: string, scrapedData: any, wikitext: string): { present: boolean; details: string; quality?: string } {
  const fruitData = scrapedData.fruitData;

  switch (section) {
    case 'Basic Info (Infobox)':
      return {
        present: !!(fruitData.type && fruitData.rarity),
        details: `Type: ${fruitData.type || 'Missing'}, Rarity: ${fruitData.rarity || 'Missing'}`
      };

    case 'Price Data':
      return {
        present: !!(fruitData.priceData?.current?.money && fruitData.priceData?.current?.robux),
        details: `Money: ${fruitData.priceData?.current?.money || 'Missing'}, Robux: ${fruitData.priceData?.current?.robux || 'Missing'}`
      };

    case 'Stats Data':
      const statsCount = Object.keys(fruitData.statsData || {}).length;
      return {
        present: statsCount > 0,
        details: `${statsCount} stat forms extracted`,
        quality: statsCount >= 3 ? 'Complete' : 'Partial'
      };

    case 'Abilities/Moveset':
      const skillCount = Object.keys(fruitData.skillData || {}).length;
      return {
        present: skillCount > 0,
        details: `${skillCount} skill forms extracted`,
        quality: skillCount >= 3 ? 'Complete' : 'Partial'
      };

    case 'Passive Abilities':
      const passiveCount = fruitData.passiveAbilities?.length || 0;
      return {
        present: passiveCount > 0,
        details: `${passiveCount} passives extracted`
      };

    case 'Overview (Pros/Cons)':
      return {
        present: !!(fruitData.pros?.length && fruitData.cons?.length),
        details: `Pros: ${fruitData.pros?.length || 0}, Cons: ${fruitData.cons?.length || 0}`
      };

    case 'Gallery':
      return {
        present: !!(fruitData.gallery?.length),
        details: `${fruitData.gallery?.length || 0} gallery items`
      };

    case 'Trivia':
      return {
        present: !!(fruitData.trivia?.length),
        details: `${fruitData.trivia?.length || 0} trivia items`
      };

    case 'Change History':
      return {
        present: !!(fruitData.changeHistoryAdvanced?.length),
        details: `${fruitData.changeHistoryAdvanced?.length || 0} history entries`
      };

    case 'Upgrade Data':
      const upgradeCount = Object.keys(fruitData.upgradeData || {}).length;
      return {
        present: upgradeCount > 0,
        details: `${upgradeCount} upgrade entries`
      };

    case 'Instinct Data':
      const instinctCount = Object.keys(fruitData.instinctData || {}).length;
      return {
        present: instinctCount > 0,
        details: `${instinctCount} instinct entries`
      };

    case 'Skin System':
      return {
        present: !!(fruitData.skinSystem),
        details: fruitData.skinSystem ? 'Present' : 'Missing'
      };

    case 'Dragon Token Info':
      return {
        present: wikitext.includes('Dragon Token') && fruitData.trivia?.some((t: string) => t.includes('Dragon Token')),
        details: 'Dragon Token section in trivia'
      };

    case 'Tips & Tricks':
      return {
        present: wikitext.includes('Tips & Tricks'),
        details: 'Tips section present in raw data but not extracted'
      };

    default:
      return { present: false, details: 'Unknown section' };
  }
}

function printReport(report: DataCompletenessReport) {
  console.log(`\n🍎 ${report.fruit.toUpperCase()} ANALYSIS`);
  console.log('=' .repeat(50));
  console.log(`Completeness: ${report.completenessPercentage.toFixed(1)}% (${report.extractedSections}/${report.totalSections})`);
  
  console.log('\n✅ EXTRACTED SECTIONS:');
  report.extractedSectionsList.forEach(section => {
    const details = report.detailedAnalysis[section];
    console.log(`  ✓ ${section}: ${details.details}`);
  });

  if (report.missingSections.length > 0) {
    console.log('\n❌ MISSING SECTIONS:');
    report.missingSections.forEach(section => {
      const details = report.detailedAnalysis[section];
      console.log(`  ✗ ${section}: ${details.details}`);
    });
  }
}

analyzeDataCompleteness().catch(console.error);
